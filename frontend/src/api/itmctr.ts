import request from '@/utils/request'

import {
    FormDefinitionDto,
    FormInstanceDto,
    FormInstanceJsonDto,
    UserFormInstancePageQueryParams
} from "@/dtos/dynamic-form.dto";
import { getApiBaseUrl } from '@/config/env';
import {IndexProjectCountRequestDto} from "@/dtos/itmctr";
import { PageResult } from '@/dtos';
import { FormInstancePageQueryParams } from '@/dtos/dynamic-form-mgt.dto';
const baseURL = getApiBaseUrl('itmctr');



export function getProjectDefinition() {
    return request<FormDefinitionDto>({
        url: `${baseURL}/Project/define`,
        method: 'get'
    })
}

export function getProject(businessId: string) {
    return request<FormDefinitionDto>({
        url: `${baseURL}/Project/newest/${businessId}`,
        method: 'get'
    })
}
export function getProjectWithDiff(businessId: string) {
    return request<FormDefinitionDto>({
        url: `${baseURL}/Project/diff/${businessId}`,
        method: 'get'
    })
}


export function createExtractProject(taskId: string) {
    return request<string>({
        url: `${baseURL}/Project/create/${taskId}`,
        method: 'post'
    })
}


export function createProject(formDefinition: FormDefinitionDto) {
    return request<string>({
        url: `${baseURL}/Project/save`,
        method: 'post',
        data: formDefinition
    })
}

export function saveProject(businessId: string, formDefinition: FormDefinitionDto) {
    return request<string>({
        url: `${baseURL}/Project/save/${businessId}`,
        method: 'post',
        data: formDefinition
    })
}

export function submitProject(businessId: string) {
    return request<string>({
        url: `${baseURL}/Project/submit/${businessId}`,
        method: 'post',
        timeout: 30000,
    })
}

export function getIndexProjectCount(data:IndexProjectCountRequestDto) {
    return request<Record<string, number>>({
        url: `${baseURL}/Project/indexProjectCount`,
        method: 'post',
        data: data
    })
}


/**
 * 分页获取表单实例
 * @param params 查询参数
 * @returns 分页结果
 */
export function getSearchInstancePage(params: UserFormInstancePageQueryParams) {
  return request<FormInstanceDataDto>({
    url: `${baseURL}/Project/search`,
    method: 'get',
    params
  })
}

export interface FormInstanceDataDto {
  rows: FormInstanceJsonDto[]
  formDefinition: FormDefinitionDto
  totals: number
}