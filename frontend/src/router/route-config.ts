import {RouteRecordRaw} from 'vue-router'
// 使用函数导入Layout组件，避免循环依赖
const getLayout = () => import('@/components/layout/index.vue')

/**
 * 路由配置类
 * 用于管理系统的三部分路由：
 * 1. 公共路由（不需要登录）
 * 2. 基础路由（需要登录但不需要权限）
 * 3. 动态路由（需要登录且需要权限，从后端获取）
 */
export class RouteConfig {
    /**
     * 公共路由
     * 所有用户都可以访问的路由，不需要登录
     */
    static publicRoutes: RouteRecordRaw[] = [
        {
            path: '/search',
            component: () => import('@/views/project/search.vue'),
            name: 'searchIndex',
            meta: {
                inMenu: true,
                openInNewTab: true,
                title: '项目检索',
                icon: 'el-icon-search',
                order: 4
            }
        },
        {
            path: '/login',
            name: 'login',
            component: () => import('@/views/login/index.vue'),
            meta: {title: '登录'}
        },
        
        {
            path: '/forgot-password',
            component: () => import('@/views/forgot-password/index.vue'),
            meta: {title: '忘记密码'}
        },
        {
            path: '/404',
            component: () => import('@/views/error/404.vue'),
            meta: {title: '404', hidden: true}
        },
        {
            path: '/403',
            component: () => import('@/views/error/403.vue'),
            meta: {title: '403', hidden: true}
        },
        {
            path: '/register',
            component: () => import('@/views/register/index.vue'),
            meta: {title: '注册', hidden: true}
        }
    ]

    /**
     * 基础路由
     * 登录后默认可访问的路由，不需要特殊权限
     */
    static baseRoutes: RouteRecordRaw[] = [
        {
            path: '/',
            component: getLayout,
            redirect: '/dashboard',
            name: 'Root',
            meta: {
                title: '首页', icon: 'el-icon-menu', order: 1
            },
            children: [
                {
                    path: 'dashboard',
                    component: () => import('@/views/dashboard/index.vue'),
                    name: 'dashboard',
                    meta: {title: '首页', icon: 'el-icon-menu', hideInMenu: true}
                },
            ]
        },

        {
            path: '/review',
            component: () => import('@/views/review.vue'),
            name: 'reviewRule',
            meta: {
                openInNewTab: true,
                title: '初级临床试验审核工作规程',
                icon: 'el-icon-menu',
                order: 97,
                hideInMenu: true
            }
        },

        {
            path: '/project',
            component: getLayout,
            name: 'project',
            meta: {title: '项目中心', icon: 'el-icon-menu', order: 2, hideInMenu: true},
            children: [
                {
                    path: '/project/user/project-extract',
                    component: () => import('@/views/project/user/project-extract.vue'),
                    name: 'projectUserProjectExtract',
                    meta: {title: '提取新项目', hideInMenu: true}
                },
                {
                    path: '/project/user/project-add/:businessId',
                    component: () => import('@/views/project/user/project-add.vue'),
                    name: 'projectUserProjectAddEdit',
                    meta: {title: '修改项目', hideInMenu: true}
                },
                {
                    path: '/project/user/project-view/:businessId',
                    component: () => import('@/views/project/project-view.vue'),
                    name: 'projectUserProjectView',
                    meta: {title: '查看项目', hideInMenu: true}
                }
            ]
        },
        {
            path: '/profile',
            component: getLayout,
            redirect: '/profile/index',
            meta: {title: '个人中心', icon: 'el-icon-user', order: 99},
            children: [
                {
                    path: '/profile/index',
                    component: () => import('@/views/profile/index.vue'),
                    name: 'Profile',
                    meta: {title: '个人资料', icon: 'el-icon-user'}
                },
                {
                    path: '/profile/change-password',
                    component: () => import('@/views/profile/change-password.vue'),
                    name: 'ChangePassword',
                    meta: {title: '修改密码', icon: 'el-icon-key'}
                }
            ]
        },

    ]


    static permissionRoutes: RouteRecordRaw[] = [
        {
            path: '/project/user/add',
            component: getLayout,
            redirect: '/project/user/project-add',
            name: 'projectUserAdd',
            meta: {
                // openInNewTab: true,
                title: '注册新项目', icon: 'el-icon-circle-plus', order: 1
            },
            children: [
                {
                    path: '/project/user/project-add',
                    component: () => import('@/views/project/user/project-add.vue'),
                    name: 'projectUserProjectAdd',
                    meta: {title: '注册新项目', hideInMenu: true}
                }
            ]
        },
        {
            path: '/project',
            component: getLayout,
            name: 'project',
            meta: {title: '项目中心', icon: 'el-icon-menu', order: 2},
            children: [
                {
                    path: '/project/user/all-list',
                    component: () => import('@/views/project/user/all-list.vue'),
                    name: 'projectUserAllList',
                    meta: {title: '我的项目'}
                },
                {
                    path: '/project/user/pending-submit',
                    component: () => import('@/views/project/user/pending-submit.vue'),
                    name: 'projectUserPendingSubmit',
                    meta: {title: '待提交项目'}
                },
                {
                    path: '/project/user/pending-approval',
                    component: () => import('@/views/project/user/pending-approval.vue'),
                    name: 'projectUserPendingApproval',
                    meta: {title: '待审核项目'}
                },
                {
                    path: '/project/user/approved-list',
                    component: () => import('@/views/project/user/approved-list.vue'),
                    name: 'projectUserApprovedList',
                    meta: {title: '已通过项目'}
                },
                {
                    path: '/project/user/project-extract',
                    component: () => import('@/views/project/user/project-extract.vue'),
                    name: 'projectUserProjectExtract',
                    meta: {title: '提取新项目', hideInMenu: true}
                }

            ]
        },
        {
            path: '/project-approval',
            component: getLayout,
            name: 'projectApproval',
            meta: {title: '项目审核', icon: 'el-icon-check', order: 3},
            children: [

                //  一级
                {
                    path: '/project/system/pending-judge-list',
                    component: () => import('@/views/project/system/pending-judge-list.vue'),
                    name: 'projectSystemPendingJudgeList',
                    meta: {title: '待判断项目', order: 1}
                },
                {
                    path: '/project/system/pending-send-number-list',
                    component: () => import('@/views/project/system/pending-send-number-list.vue'),
                    name: 'projectSystemPendingSendNumberList',
                    meta: {title: '待发号项目', order: 2}
                },
                {
                    path: '/project/system/apply-edit-list',
                    component: () => import('@/views/project/system/apply-edit-list.vue'),
                    name: 'projectSystemApplyEditList',
                    meta: {title: '再修改申请列表', order: 3}
                },
                {
                    path: '/project/system/pending-review-list-1',
                    component: () => import('@/views/project/system/pending-review-list-1.vue'),
                    name: 'projectSystemPendingReviewList',
                    meta: {title: '再修改复核项目', order: 4}
                },
                {
                    path: '/project/system/return-edit-list',
                    component: () => import('@/views/project/system/return-edit-list.vue'),
                    name: 'projectSystemReturnEditList',
                    meta: {title: '再修改退回列表', order: 5}
                }, {
                    path: '/project/system/approved-list',
                    component: () => import('@/views/project/system/approved-list.vue'),
                    name: 'projectSystemApprovedList',
                    meta: {title: '已发号项目', order: 6}
                },
                {
                    path: '/project/system/non-traditional-list',
                    component: () => import('@/views/project/system/non-traditional-list.vue'),
                    name: 'projectSystemNonTraditionalList',
                    meta: {title: '非传统医学项目', order: 7}
                },

                {
                    path: '/project/system/all-submitted-list',
                    component: () => import('@/views/project/system/all-submitted-list.vue'),
                    name: 'projectSystemAllSubmittedList',
                    meta: {title: '审核状态查询', order: 8}
                },
                {
                    path: '/project/system/project-judge/:businessId',
                    component: () => import('@/views/project/system/project-judge.vue'),
                    name: 'projectSystemProjectJudge',
                    meta: {title: '判断项目', hideInMenu: true}
                },
                {
                    path: '/project/system/project-review-edit/:businessId',
                    component: () => import('@/views/project/system/project-review-edit.vue'),
                    name: 'projectSystemProjectReviewEdit',
                    meta: {title: '再修改项目审核', hideInMenu: true}
                },
                {
                    path: '/project/system/project-send-number/:businessId',
                    component: () => import('@/views/project/system/project-send-number.vue'),
                    name: 'projectSystemProjectSendNumber',
                    meta: {title: '审核项目', hideInMenu: true}
                },


//二级
                {
                    path: '/project/system/pending-assign-list',
                    component: () => import('@/views/project/system/pending-assign-list.vue'),
                    name: 'projectSystemPendingAssignList',
                    meta: {title: '待分配项目', order: 9}
                },
                {
                    path: '/project/system/pending-review-list-2',
                    component: () => import('@/views/project/system/pending-review-list-2.vue'),
                    name: 'projectSystemPendingReviewList2',
                    meta: {title: '待核审项目', order: 10}
                },
                {
                    path: '/project/system/review-returned-list-2',
                    component: () => import('@/views/project/system/review-returned-list-2.vue'),
                    name: 'projectSystemReviewReturnedList2',
                    meta: {title: '已退回项目', order: 11}
                },
                {
                    path: '/project/system/pending-approved-list-2',
                    component: () => import('@/views/project/system/pending-approved-list-2.vue'),
                    name: 'projectSystemPendingApprovedList2',
                    meta: {title: '已核审通过项目', order: 12}
                },
                {
                    path: '/project/system/approved-list-2',
                    component: () => import('@/views/project/system/approved-list-2.vue'),
                    name: 'projectSystemApprovedList2',
                    meta: {title: '已发号项目', order: 13}
                },

                {
                    path: '/project/system/re-assign-list',
                    component: () => import('@/views/project/system/re-assign-list.vue'),
                    name: 'projectSystemReAssignList',
                    meta: {title: '重新分配项目', order: 14}
                },
                {
                    path: '/project/system/project-review-2/:businessId',
                    component: () => import('@/views/project/system/project-review-2.vue'),
                    name: 'projectSystemProjectReview2',
                    meta: {title: '审核项目', hideInMenu: true}
                },
                {
                    path: '/project/system/project-assign/:businessId',
                    component: () => import('@/views/project/system/project-assign.vue'),
                    name: 'projectSystemProjectAssign',
                    meta: {title: '分配项目', hideInMenu: true}
                },

//三级
                {
                    path: '/project/system/pending-assign-review-list',
                    component: () => import('@/views/project/system/pending-assign-review-list.vue'),
                    name: 'projectSystemPendingAssignReviewList',
                    meta: {title: '待分配项目', order: 15}
                },
                {
                    path: '/project/system/pending-review-list-3',
                    component: () => import('@/views/project/system/pending-review-list-3.vue'),
                    name: 'projectSystemPendingReviewList3',
                    meta: {title: '待复审项目', order: 16}
                },
                {
                    path: '/project/system/pending-approved-list-3',
                    component: () => import('@/views/project/system/pending-approved-list-3.vue'),
                    name: 'projectSystemPendingApprovedList3',
                    meta: {title: '已复审通过项目', order: 17}
                },
                {
                    path: '/project/system/review-returned-list-3',
                    component: () => import('@/views/project/system/review-returned-list-3.vue'),
                    name: 'projectSystemReviewReturnedList3',
                    meta: {title: '已退回项目', order: 18}
                },
                {
                    path: '/project/system/approved-list-3',
                    component: () => import('@/views/project/system/approved-list-3.vue'),
                    name: 'projectSystemApprovedList3',
                    meta: {title: '已发号项目', order: 19}
                },
                {
                    path: '/project/system/project-assign-review/:businessId',
                    component: () => import('@/views/project/system/project-assign-review.vue'),
                    name: 'projectSystemProjectAssignReview',
                    meta: {title: '分审项目', hideInMenu: true}
                },
                {
                    path: '/project/system/project-review-3/:businessId',
                    component: () => import('@/views/project/system/project-review-3.vue'),
                    name: 'projectSystemProjectReview3',
                    meta: {title: '审核项目', hideInMenu: true}
                },


//四级
                {
                    path: '/project/system/pending-review-list-4',
                    component: () => import('@/views/project/system/pending-review-list-4.vue'),
                    name: 'projectSystemPendingReviewList4',
                    meta: {title: '待初审项目', order: 20}
                },
                {
                    path: '/project/system/pending-approved-list-4',
                    component: () => import('@/views/project/system/pending-approved-list-4.vue'),
                    name: 'projectSystemPendingApprovedList4',
                    meta: {title: '已初审通过项目', order: 21}
                },
                {
                    path: '/project/system/review-returned-list-4',
                    component: () => import('@/views/project/system/review-returned-list-4.vue'),
                    name: 'projectSystemReviewReturnedList4',
                    meta: {title: '已退回项目', order: 22}
                },
                {
                    path: '/project/system/approved-list-4',
                    component: () => import('@/views/project/system/approved-list-4.vue'),
                    name: 'projectSystemApprovedList4',
                    meta: {title: '已发号项目', order: 23}
                },
                {
                    path: '/project/system/project-review-4/:businessId',
                    component: () => import('@/views/project/system/project-review-4.vue'),
                    name: 'projectSystemProjectReview4',
                    meta: {title: '审核项目', hideInMenu: true}
                },


            ]
        },
        {
            path: '/system',
            component: getLayout,
            name: 'system',
            meta: {title: '系统管理', icon: 'el-icon-setting', order: 10},
            redirect: '/system/user',
            children: [
                {
                    path: '/system/user',
                    name: 'system:user',
                    component: () => import('@/views/system/user/index.vue'),
                    meta: {title: '用户管理', icon: 'el-icon-user', order: 1}
                },
                {
                    path: '/system/role',
                    name: 'system:role',
                    component: () => import('@/views/system/role/index.vue'),
                    meta: {title: '角色管理', icon: 'el-icon-key', order: 2}
                },
                {
                    path: '/system/organization',
                    name: 'system:organization',
                    component: () => import('@/views/system/organization/index.vue'),
                    meta: {title: '组织管理', icon: 'el-icon-office-building', order: 3}
                },
                {
                    path: '/system/position',
                    name: 'system:position',
                    component: () => import('@/views/system/position/index.vue'),
                    meta: {title: '岗位管理', icon: 'el-icon-user-filled', order: 4}
                },
                {
                    path: '/system/permission',
                    name: 'system:permission',
                    component: () => import('@/views/system/permission/index.vue'),
                    meta: {title: '权限管理', icon: 'el-icon-key', order: 5}
                }
            ]
        },
        {
            path: '/files',
            name: 'files',
            component: getLayout,
            meta: {title: '存储管理', icon: 'el-icon-folder', order: 11},
            redirect: '/files/storage',
            children: [
                {
                    path: '/files/storage',
                    name: 'files:storage',
                    component: () => import('@/views/files/storage/index.vue'),
                    meta: {title: '存储管理', icon: 'el-icon-folder', order: 1}
                },
                {
                    path: '/files/file-type',
                    name: 'files:file-type',
                    component: () => import('@/views/files/file-type/index.vue'),
                    meta: {title: '文件类型管理', icon: 'el-icon-document', order: 2}
                }
            ]
        },
        {
            path: '/form-management',
            name: 'form-management',
            component: getLayout,
            meta: {title: '表单管理', icon: 'el-icon-document', order: 12},
            children: [
                {
                    path: '/form-management/form-list',
                    name: 'form-management:form-list',
                    component: () => import('@/views/form-management/form-list/index.vue'),
                    meta: {title: '表单定义', icon: 'el-icon-document-copy', order: 1}
                },
                {
                    path: '/form-management/form-designer/:id',
                    name: 'form-management:design',
                    component: () => import('@/views/form-management/form-list/form-designer.vue'),
                    meta: {title: '表单设计', icon: 'el-icon-document-copy', order: 2, hideInMenu: true}
                }
            ]
        }
        // 其它如 API 权限根节点等可根据需要补充
    ]

    /**
     * 所有不需要动态判断权限的路由
     * 包括公共路由和基础路由
     */
    static constantRoutes: RouteRecordRaw[] = [
        ...RouteConfig.publicRoutes,
        ...RouteConfig.baseRoutes
    ]

    /**
     * 所有路由的末尾路由
     * 必须放在最后，用于捕获所有未匹配的路由
     */
    static fallbackRoute: RouteRecordRaw = {
        path: '/:pathMatch(.*)*',
        redirect: '/404',
        meta: {hidden: true}
    }

    /**
     * 白名单路由
     * 未登录也可以访问的路由
     */
    // static whiteList: string[] = ['/login', '/internal/login', '/register', '/forgot-password', '/404']
}
