<template>
  <div class="form-designer-layout">
    <div class="form-designer-left">
      <FieldTree
          :fieldTypes="fieldTypes"
          current-container-type=""
          @dragstart="onFieldTreeDragStart"
      />
    </div>
    <div class="form-designer-right">
      <div class="designer-toolbar">
        <el-button type="primary" @click="showPreview = true"
        >预览/导出 JSON
        </el-button>
        <el-button @click="handleMockValidation" type="primary">
          模拟验证
        </el-button>
        <el-button type="success" @click="saveForm" style="margin-left: 12px"
        >保存
        </el-button>
      </div>

      <el-tabs v-model="activeTab" style="margin-top: 16px">
        <el-tab-pane label="表单设计" name="design">
          <el-select v-model="renderMode" style="width: 160px">
            <el-option label="设计视图" value="design"/>
            <el-option label="编辑视图" value="edit"/>
            <el-option label="查看视图" value="view"/>
            <el-option label="审批视图" value="approval"/>
          </el-select>
          <FormCanvas
              :formSchema="formSchema"
              :render-mode="renderMode"
              ref="formCanvasRef"
              @update:field="onUpdateField"
          />
        </el-tab-pane>
        <el-tab-pane label="表单行为" name="behavior">
          <div>
            <el-button type="primary" size="small" @click="onAddBehavior"
            >新增行为项
            </el-button>
            <el-table
                :data="behaviorList"
                style="margin-top: 12px"
                size="small"
                border
            >
              <el-table-column prop="code" label="目标字段编码" width="220">
                <template #default="scope">
                  <span>
                    {{ scope.row.code }}
                    <span v-if="getFieldLabelZh(scope.row.code)"
                    >（{{ getFieldLabelZh(scope.row.code) }}）</span
                    >
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="显示规则">
                <template #default="scope">
                  <div
                      v-for="(item, idx) in scope.row.showFields"
                      :key="idx"
                      class="behavior-rule-cell"
                  >
                    <div class="behavior-condition">
                      条件: {{ showConditionText(item.condition) }}
                    </div>
                    <div class="behavior-fields">
                      <el-tag
                          v-for="f in item.fields"
                          :key="f"
                          size="small"
                          style="margin-right: 4px; margin-bottom: 2px"
                      >
                        {{
                          f
                        }}<span v-if="getFieldLabelZh(f)"
                      >（{{ getFieldLabelZh(f) }}）</span
                      >
                      </el-tag>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="隐藏规则">
                <template #default="scope">
                  <div
                      v-for="(item, idx) in scope.row.hideFields"
                      :key="idx"
                      class="behavior-rule-cell"
                  >
                    <div class="behavior-condition">
                      条件: {{ showConditionText(item.condition) }}
                    </div>
                    <div class="behavior-fields">
                      <el-tag
                          v-for="f in item.fields"
                          :key="f"
                          size="small"
                          type="info"
                          style="margin-right: 4px; margin-bottom: 2px"
                      >
                        {{
                          f
                        }}<span v-if="getFieldLabelZh(f)"
                      >（{{ getFieldLabelZh(f) }}）</span
                      >
                      </el-tag>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120">
                <template #default="scope">
                  <el-button size="small" @click="onEditBehavior(scope.$index)"
                  >编辑
                  </el-button>
                  <el-button
                      size="small"
                      type="danger"
                      @click="onDeleteBehavior(scope.$index)"
                  >删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <el-dialog
              v-model="behaviorDialogVisible"
              title="行为项编辑"
              width="00px"
          >
            <el-form :model="editBehavior" label-width="100px">
              <el-form-item label="目标字段编码">
                <el-select
                    v-model="editBehavior.code"
                    filterable
                    placeholder="请选择目标字段"
                    style="width: 100%"
                >
                  <el-option
                      v-for="f in allFields"
                      :key="f.code"
                      :label="
                      (f.labelZh || f.labelEn || f.code) +
                      (f.code ? '（' + f.code + '）' : '')
                    "
                      :value="f.code"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="显示规则">
                <div
                    v-for="(item, idx) in editBehavior.showFields"
                    :key="'show' + idx"
                    style="
                    margin-bottom: 8px;
                    border-bottom: 1px dashed #eee;
                    padding-bottom: 6px;
                  "
                >
                  <el-row :gutter="8">
                    <el-col :span="6">
                      <el-input
                          v-model="item.property"
                          placeholder="属性名(可选)"
                          size="small"
                      />
                    </el-col>
                    <el-col :span="8">
                      <el-select
                          v-model="item.conditionType"
                          placeholder="条件类型"
                          size="small"
                          style="width: 100%"
                      >
                        <el-option label="等于" value="equals"/>
                        <el-option label="不等于" value="notEquals"/>
                      </el-select>
                    </el-col>
                    <el-col :span="6">
                      <el-input
                          v-model="item.conditionValue"
                          placeholder="条件值"
                          size="small"
                      />
                    </el-col>
                    <el-col :span="4">
                      <el-button
                          size="small"
                          type="danger"
                          @click="removeShowField(idx)"
                      >删除
                      </el-button>
                    </el-col>
                  </el-row>
                  <el-row style="margin-top: 4px">
                    <el-col :span="24">
                      <el-select
                          v-model="item.fields"
                          multiple
                          filterable
                          placeholder="影响字段"
                          size="small"
                          style="width: 100%"
                      >
                        <el-option
                            v-for="f in allFields"
                            :key="f.code"
                            :label="f.labelZh || f.code"
                            :value="f.code"
                        />
                      </el-select>
                    </el-col>
                  </el-row>
                </div>
                <el-button size="small" @click="addShowField"
                >新增显示规则
                </el-button>
              </el-form-item>
              <el-form-item label="隐藏规则">
                <div
                    v-for="(item, idx) in editBehavior.hideFields"
                    :key="'hide' + idx"
                    style="
                    margin-bottom: 8px;
                    border-bottom: 1px dashed #eee;
                    padding-bottom: 6px;
                  "
                >
                  <el-row :gutter="8">
                    <el-col :span="6">
                      <el-input
                          v-model="item.property"
                          placeholder="属性名(可选)"
                          size="small"
                      />
                    </el-col>
                    <el-col :span="8">
                      <el-select
                          v-model="item.conditionType"
                          placeholder="条件类型"
                          size="small"
                          style="width: 100%"
                      >
                        <el-option label="等于" value="equals"/>
                        <el-option label="不等于" value="notEquals"/>
                      </el-select>
                    </el-col>
                    <el-col :span="6">
                      <el-input
                          v-model="item.conditionValue"
                          placeholder="条件值"
                          size="small"
                      />
                    </el-col>
                    <el-col :span="4">
                      <el-button
                          size="small"
                          type="danger"
                          @click="removeHideField(idx)"
                      >删除
                      </el-button>
                    </el-col>
                  </el-row>
                  <el-row style="margin-top: 4px">
                    <el-col :span="24">
                      <el-select
                          v-model="item.fields"
                          multiple
                          filterable
                          placeholder="影响字段"
                          size="small"
                          style="width: 100%"
                      >
                        <el-option
                            v-for="f in allFields"
                            :key="f.code"
                            :label="f.labelZh || f.code"
                            :value="f.code"
                        />
                      </el-select>
                    </el-col>
                  </el-row>
                </div>
                <el-button size="small" @click="addHideField"
                >新增隐藏规则
                </el-button>
              </el-form-item>
            </el-form>
            <template #footer>
              <el-button @click="behaviorDialogVisible = false">取消</el-button>
              <el-button type="primary" @click="onSaveBehavior">保存</el-button>
            </template>
          </el-dialog>
        </el-tab-pane>
        <el-tab-pane label="表单验证" name="validation">
          <div>
            <el-button type="primary" size="small" @click="onAddValidationRule"
            >新增校验规则
            </el-button>
            <el-table
                :data="validationList"
                style="margin-top: 12px"
                size="small"
                border
            >
              <el-table-column label="触发字段" prop="triggers">
                <template #default="scope">
                  <el-tag
                      v-for="f in scope.row.triggers"
                      :key="f"
                      size="small"
                      style="margin-right: 4px"
                  >
                    {{ getFieldLabelZh(f) || f }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="错误信息(中)" prop="message.zh"/>
              <el-table-column label="错误信息(英)" prop="message.en"/>
<!--              <el-table-column label="级别" prop="level" />-->
              <el-table-column label="表达式">
                <template #default="scope">
                  <span style="font-size:12px;color:#666;">{{ formatDslExpression(scope.row.expression) }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120">
                <template #default="scope">
                  <el-button
                      size="small"
                      @click="onEditValidationRule(scope.$index)"
                  >编辑
                  </el-button>
                  <el-button
                      size="small"
                      type="danger"
                      @click="onDeleteValidationRule(scope.$index)"
                  >删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <el-dialog
              v-model="validationDialogVisible"
              title="校验规则编辑"
              width="900px"
          >

            <el-form :model="editValidationRule" label-width="100px">
              <el-form-item label="触发字段">
                <el-select
                    v-model="editValidationRule.triggers"
                    multiple
                    filterable
                    placeholder="请选择触发字段"
                    style="width: 100%"
                >
                  <el-option
                      v-for="f in allFields"
                      :key="f.code"
                      :label="f.labelZh || f.code"
                      :value="f.code"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="错误信息(中)">
                <el-input
                    v-model="editValidationRule.message.zh"
                    placeholder="请输入中文错误信息"
                />
              </el-form-item>
              <el-form-item label="错误信息(英)">
                <el-input
                    v-model="editValidationRule.message.en"
                    placeholder="请输入英文错误信息"
                />
              </el-form-item>
              <el-form-item label="级别" v-show="false">
                <el-select
                    v-model="editValidationRule.level"
                    placeholder="请选择级别"
                    style="width: 100%"
                >
                  <el-option label="错误" value="error"/>
                  <el-option label="警告" value="warning"/>
                </el-select>
              </el-form-item>
              <el-form-item label="表达式">
                <div class="expr-dialog-content">
                  <ExpressionEditor
                      v-model="editValidationRule.expression"
                      :all-fields="allFields"
                  />
                </div>
              </el-form-item>
            </el-form>
            <!-- ExpressionEditor 及其它内容 -->

            <template #footer>
              <el-button @click="validationDialogVisible = false"
              >取消
              </el-button>
              <el-button type="primary" @click="onSaveValidationRule"
              >保存
              </el-button>
            </template>
          </el-dialog>
        </el-tab-pane>
      </el-tabs>
      <div v-if="showPreview" class="json-preview-mask">
        <div class="json-preview-panel">
          <div class="json-preview-title">表单 JSON 预览/导出</div>
          <div style="margin-bottom: 10px">
            <el-switch
                v-model="showRawJson"
                active-text="原始文本"
                inactive-text="折叠视图"
            />
          </div>
          <el-input
              v-if="showRawJson"
              type="textarea"
              :readonly="true"
              :value="jsonString"
              :rows="30"
          />
          <vue-json-pretty
              v-else
              :data="serializeForm(formSchema)"
              :deep="2"
              style="
              max-height: 500px;
              overflow: auto;
              border: 1px solid #eee;
              border-radius: 4px;
            "
          />
          <div class="json-preview-footer">
            <button @click="showPreview = false">关闭</button>
            <button @click="copyJson">复制 JSON</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref, computed, watch, onMounted, reactive} from "vue";
import FieldTree from "./components/FieldTree.vue";
import FormCanvas from "./components/FormCanvas.vue";
import {ElButton, ElMessageBox} from "element-plus";
import {saveFormDefinition, getFormDefinition} from "@/api/dynamic-form-mgt";
import {ElMessage} from "element-plus";
import {useRoute, useRouter} from "vue-router";
import VueJsonPretty from "vue-json-pretty";
import "vue-json-pretty/lib/styles.css";
import ExpressionEditor from "./components/ExpressionEditor.vue";

// 可用控件类型（后续可补充）
const fieldTypes = ref([
  {type: "input", label: "单行文本"},
  {type: "textarea", label: "多行文本"},
  {type: "text_multilang", label: "多语言文本"},
  {type: "textarea_multilang", label: "多语言多行文本"},
  {type: "select", label: "下拉选择"},
  {type: "checkbox", label: "多选框"},
  {type: "radio", label: "单选框"},
  {type: "date", label: "日期"},
  {type: "number", label: "数字"},
  {type: "unit_select", label: "单位选择"},
  {type: "file", label: "文件"},
  {type: "int_range", label: "整数区间"},
  {type: "date_range", label: "日期区间"},
  {type: "subForm", label: "子表单"},
  {type: "multiSubForm", label: "多行子表单"},
  {type: "group", label: "分组"},
]);
let renderMode = ref<"design" | "edit" | "readonly" | "view">("design");
let activeTab = ref<string>("design");

interface FormField {
  id: string;
  labelZh?: string;
  labelEn?: string;
  code?: string;
  type?: string;
  fields?: FormField[];

  [key: string]: any;
}

interface FormGroup {
  id: string;
  labelZh?: string;
  labelEn?: string;
  code?: string;
  type?: string;
  fields: FormField[];

  [key: string]: any;
}

interface FormSchema {
  groups: FormGroup[];
  jsonConfig?: {
    behavior: any[];
    validation: any[];
  };
}

const formSchema = ref<FormSchema>({groups: []});

// 拖拽控件时设置 dataTransfer 数据
function onFieldTreeDragStart(type: string) {
  // @ts-ignore
  window.__dragFieldType = type;
}

const showPreview = ref(false);
const jsonString = computed(() =>
    JSON.stringify(serializeForm(formSchema.value), null, 2)
);

function copyJson() {
  navigator.clipboard.writeText(jsonString.value);
}

// 组装最终JSON
function serializeForm(formSchema: any) {
  // 深拷贝表单结构
  const schema = JSON.parse(JSON.stringify(formSchema));

  return schema;
}

function onInsertGroup() {
  // 生成唯一ID
  const uniqueId = Date.now() + "-" + Math.floor(Math.random() * 10000);
  // 生成默认编码
  const defaultCode = `grp_${uniqueId.toString()}`;

  const newGroup = {
    id: "g" + uniqueId,
    labelZh: `分组${formSchema.value.groups.length + 1}`,
    labelEn: `Group${formSchema.value.groups.length + 1}`,
    type: "group",
    code: defaultCode, // 添加默认编码
    fields: [],
  };
  formSchema.value.groups.push(newGroup);
}

// 计算所有字段列表
interface FormFieldOption {
  id: string;
  labelZh?: string;
  label?: string;
  code?: string;
  options?: { value: string; labelZh?: string; label?: string }[];
}

const allFields = computed<FormFieldOption[]>(() => {
  return formSchema.value.groups.flatMap((group: any) => group.fields || []);
});

function getFieldLabel(fieldId: string) {
  const field = allFields.value.find((f) => f.id === fieldId);
  return field ? field.labelZh || field.code || field.id : fieldId;
}

const route = useRoute();
const formId = route.params.id as string;
const router = useRouter();
const formCanvasRef = ref();

async function handleMockValidation() {
  const result = formCanvasRef.value?.validateAll?.();
  if (!result?.valid) {
    const messages = result.messages.filter((q: any) => q.message);

    // 1. 普通字段错误
    const normalErrors: string[] = [];
    // 2. 子表格错误，分组：parent.code + rowIndex
    const subformErrorMap: Record<
        string,
        {
          parentLabel: string;
          rowIndex: number;
          errors: string[];
        }
    > = {};

    for (const e of messages) {
      if (e.rowIndex == null && e.parent == null) {
        // 普通字段
        normalErrors.push(
            `${
                formSchema.value.language == "both" ||
                formSchema.value.language == "zh"
                    ? e.labelZh
                    : e.labelEn
            }：${e.message}`
        );
      } else if (e.rowIndex != null && e.parent) {
        // 子表格字段
        const key = `${e.parent.code}_${e.rowIndex}`;
        if (!subformErrorMap[key]) {
          subformErrorMap[key] = {
            parentLabel:
                formSchema.value.language == "both" ||
                formSchema.value.language == "zh"
                    ? e.parent.labelZh
                    : e.parent.labelEn,
            rowIndex: e.rowIndex,
            errors: [],
          };
        }
        subformErrorMap[key].errors.push(
            `&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;${
                formSchema.value.language == "both" ||
                formSchema.value.language == "zh"
                    ? e.labelZh
                    : e.labelEn
            }：${e.message}`
        );
      }
    }
    // 拼接子表格错误
    const subformErrors: string[] = [];
    for (const key in subformErrorMap) {
      const group = subformErrorMap[key];
      // 行号一般用户习惯从1开始
      const rowNum = (group.rowIndex ?? 0) + 1;
      const rowErrorMsg = group.errors.join("<br/>");
      subformErrors.push(
          `${group.parentLabel} #${rowNum}：<br/>${rowErrorMsg}`
      );
    }

    // 合并所有错误
    const allErrors = [...normalErrors, ...subformErrors];

    console.log(allErrors);

    const errorHtml = `
      <div style="max-height:520px;overflow-y:auto;padding-right:8px;">
        ${allErrors.join("<br/>")}
      </div>
    `;

    await ElMessageBox.alert(
        errorHtml,
        "表单校验未通过/The form validation failed",
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: "确定",
          customClass: "form-validate-error-box",
        }
    );
    return;
  }
}

async function saveForm() {
  try {
    const body = JSON.parse(JSON.stringify(formSchema.value));
    await saveFormDefinition(formId, body);
    ElMessage.success("保存成功");
    // 跳转到列表页
    router.push({name: "form-management:form-list"}); // 如路由名称不同请自行调整
  } catch (e: any) {
    ElMessage.error("保存失败: " + (e?.message || e));
  }
}

const showRawJson = ref(false);

onMounted(async () => {
  try {
    const rsp = await getFormDefinition(formId);
    formSchema.value = rsp.data;
    ensureJsonConfig();
    if (formSchema.value.groups.length == 0) {
      onInsertGroup();
    }
  } catch (e: any) {
    ElMessage.error("加载表单定义失败: " + (e?.message || e));
  }
});

// ========== 表单行为相关 =============

interface BehaviorFieldRule {
  property?: string;
  condition: Record<string, any>;
  fields: string[];
}

interface BehaviorFieldRuleEdit {
  property?: string;
  conditionType: string;
  conditionValue: string;
  fields: string[];
}

interface BehaviorItem {
  code: string;
  showFields: BehaviorFieldRule[];
  hideFields: BehaviorFieldRule[];
}

interface BehaviorItemEdit {
  code: string;
  showFields: BehaviorFieldRuleEdit[];
  hideFields: BehaviorFieldRuleEdit[];
}

interface JsonConfig {
  behavior: BehaviorItem[];
}

function ensureJsonConfig() {
  if (!formSchema.value.jsonConfig) {
    formSchema.value.jsonConfig = {behavior: [], validation: []};
  } else {
    if (!formSchema.value.jsonConfig.behavior)
      formSchema.value.jsonConfig.behavior = [];
    if (!formSchema.value.jsonConfig.validation)
      formSchema.value.jsonConfig.validation = [];
  }
}

const behaviorDialogVisible = ref(false);
const editBehavior = reactive<BehaviorItemEdit>({
  code: "",
  showFields: [],
  hideFields: [],
});
const editIndex = ref<number>(-1);

const behaviorList = computed<BehaviorItem[]>(() => {
  ensureJsonConfig();
  return formSchema.value.jsonConfig?.behavior || [];
});

function showConditionText(condition: Record<string, any>) {
  if (!condition) return "";
  const key = Object.keys(condition)[0];
  const val = condition[key];
  if (key === "equals") return `等于 ${val}`;
  if (key === "notEquals") return `不等于 ${val}`;
  return `${key}: ${val}`;
}

function onAddBehavior() {
  editBehavior.code = "";
  editBehavior.showFields = [];
  editBehavior.hideFields = [];
  editIndex.value = -1;
  behaviorDialogVisible.value = true;
}

const onUpdateField = (field: any) => {
  console.log("字段更新:", field);
  // 实现字段更新逻辑
};

function onEditBehavior(idx: number) {
  const item = behaviorList.value[idx];
  editBehavior.code = item.code || "";
  editBehavior.showFields = (item.showFields || []).map((f) => ({
    property: f.property || "",
    conditionType: Object.keys(f.condition)[0] || "equals",
    conditionValue: Object.values(f.condition)[0] || "",
    fields: Array.isArray(f.fields) ? f.fields : [],
  }));
  editBehavior.hideFields = (item.hideFields || []).map((f) => ({
    property: f.property || "",
    conditionType: Object.keys(f.condition)[0] || "notEquals",
    conditionValue: Object.values(f.condition)[0] || "",
    fields: Array.isArray(f.fields) ? f.fields : [],
  }));
  editIndex.value = idx;
  behaviorDialogVisible.value = true;
}

function onDeleteBehavior(idx: number) {
  ensureJsonConfig();
  formSchema.value.jsonConfig!.behavior.splice(idx, 1);
}

function onSaveBehavior() {
  ensureJsonConfig();
  const newItem: BehaviorItem = {
    code: editBehavior.code,
    showFields: (editBehavior.showFields as BehaviorFieldRuleEdit[]).map(
        (f) => ({
          property: f.property,
          condition: {[f.conditionType]: f.conditionValue},
          fields: [...f.fields],
        })
    ),
    hideFields: (editBehavior.hideFields as BehaviorFieldRuleEdit[]).map(
        (f) => ({
          property: f.property,
          condition: {[f.conditionType]: f.conditionValue},
          fields: [...f.fields],
        })
    ),
  };
  if (editIndex.value === -1) {
    formSchema.value.jsonConfig!.behavior.push(newItem);
  } else {
    formSchema.value.jsonConfig!.behavior.splice(editIndex.value, 1, newItem);
  }
  behaviorDialogVisible.value = false;
}

function addShowField() {
  (editBehavior.showFields as BehaviorFieldRuleEdit[]).push({
    property: "",
    conditionType: "equals",
    conditionValue: "",
    fields: [],
  });
}

function removeShowField(idx: number) {
  (editBehavior.showFields as BehaviorFieldRuleEdit[]).splice(idx, 1);
}

function addHideField() {
  (editBehavior.hideFields as BehaviorFieldRuleEdit[]).push({
    property: "",
    conditionType: "notEquals",
    conditionValue: "",
    fields: [],
  });
}

function removeHideField(idx: number) {
  (editBehavior.hideFields as BehaviorFieldRuleEdit[]).splice(idx, 1);
}

function getFieldLabelZh(code: string): string | undefined {
  // 遍历所有分组下的fields
  for (const group of formSchema.value.groups) {
    if (group.fields) {
      for (const field of group.fields) {
        if (field.code === code) {
          return field.labelZh || field.labelEn || "";
        }
      }
    }
  }
  return "";
}

// 校验规则相关
interface ValidationRule {
  triggers: string[];
  expression: any;
  message: { zh: string; en: string };
  level?: "error" | "warning";
}

function ensureValidationConfig() {
  ensureJsonConfig();
}

const validationDialogVisible = ref(false);
const editValidationRule = reactive<ValidationRule>({
  triggers: [],
  expression: {},
  message: {zh: "", en: ""},
  level: "error",
});
const editValidationIndex = ref(-1);

const validationList = computed<ValidationRule[]>(() => {
  ensureValidationConfig();
  return formSchema.value.jsonConfig?.validation || [];
});

function onAddValidationRule() {
  editValidationRule.triggers = [];
  editValidationRule.expression = {};
  editValidationRule.message = {zh: "", en: ""};
  editValidationRule.level = "error";
  editValidationIndex.value = -1;
  validationDialogVisible.value = true;
}

function onEditValidationRule(idx: number) {
  const item = validationList.value[idx];
  editValidationRule.triggers = [...(item.triggers || [])];
  editValidationRule.expression = JSON.parse(
      JSON.stringify(item.expression || {})
  );
  editValidationRule.message.zh = item.message.zh || "";
  editValidationRule.message.en = item.message.en || "";
  editValidationRule.level = item.level || "error";
  editValidationIndex.value = idx;
  validationDialogVisible.value = true;
}

function onDeleteValidationRule(idx: number) {
  ensureJsonConfig();
  formSchema.value.jsonConfig!.validation.splice(idx, 1);
}

function onSaveValidationRule() {
  ensureJsonConfig();
  const newItem: ValidationRule = {
    triggers: [...editValidationRule.triggers],
    expression: JSON.parse(JSON.stringify(editValidationRule.expression)),
    message: {
      zh: editValidationRule.message.zh,
      en: editValidationRule.message.en,
    },
    level: editValidationRule.level,
  };
  if (editValidationIndex.value === -1) {
    formSchema.value.jsonConfig!.validation.push(newItem);
  } else {
    formSchema.value.jsonConfig!.validation.splice(
        editValidationIndex.value,
        1,
        newItem
    );
  }
  validationDialogVisible.value = false;
}

// DSL表达式转可读文本
function formatDslExpression(expr: any): string {
  if (expr == null) return '';
  if (typeof expr === 'string' || typeof expr === 'number' || typeof expr === 'boolean') return String(expr);
  if (Array.isArray(expr)) return expr.map(formatDslExpression).join(', ');
  if (typeof expr === 'object') {
    if (expr.if !== undefined) {
      return `如果(${formatDslExpression(expr.if)})，则(${formatDslExpression(expr.then)})，否则(${formatDslExpression(expr.else)})`;
    }
    if (expr.and !== undefined) {
      return expr.and.map(formatDslExpression).join(' 且 ');
    }
    if (expr.or !== undefined) {
      return expr.or.map(formatDslExpression).join(' 或 ');
    }
    if (expr.property !== undefined) {
      return `字段:${expr.property[0]}${expr.property[1] ? '.' + expr.property[1] : ''}${expr.type ? '（' + expr.type + '）' : ''}`;
    }
    if (expr.func !== undefined) {
      return `${expr.func}(${(expr.args || []).map(formatDslExpression).join(', ')})`;
    }
    // 比较操作符
    const cmp = ['==', '!=', '>', '>=', '<', '<='].find(k => expr[k] !== undefined);
    if (cmp) {
      return `${formatDslExpression(expr[cmp][0])} ${cmp} ${formatDslExpression(expr[cmp][1])}`;
    }
  }
  return JSON.stringify(expr);
}
</script>

<style scoped>
.form-designer-layout {
  display: flex;
  height: 100%;
}

.form-designer-left {
  width: 260px;
  border-right: 1px solid #eee;
  background: #fafbfc;
  padding: 12px 0;
}

.form-designer-right {
  flex: 1;
  padding: 16px;
  overflow: auto;
}

.designer-toolbar {
  margin-bottom: 12px;
}

.json-preview-mask {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.2);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.json-preview-panel {
  background: #fff;
  border-radius: 8px;
  min-width: 680px;
  min-height: 320px;
  padding: 24px 20px 16px 20px;
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.12);
  display: flex;
  flex-direction: column;
}

.json-preview-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 12px;
}

.json-preview-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.behavior-rule-cell {
  margin-bottom: 8px;
  white-space: pre-line;
  word-break: break-all;
}

.behavior-condition {
  font-weight: 500;
  color: #666;
  margin-bottom: 2px;
}

.behavior-fields {
  display: flex;
  flex-wrap: wrap;
}

.el-table th {
  font-weight: bold;
  text-align: center;
}

.el-table .el-button + .el-button {
  margin-left: 8px;
}

.expr-dialog-content {
  max-height: 380px;
  overflow-y: auto;
  min-width: 600px;
  box-sizing: border-box;
}
</style>
