<template>
  <div
      class="group-block"
      :class="{ 'drag-over': dragOver }"
      @dragover.prevent.stop="onDragOver"
      @dragleave.stop="onDragLeave"
      @drop="onDropInsert($event)"
      @drop.stop="onDrop"
  >
    <div class="group-title">
      <span v-if="language === 'both'"
      >{{ group.labelZh }}/{{ group.labelEn }}</span
      >
      <span v-else-if="language === 'zh'">{{ group.labelZh }}</span>
      <span v-else-if="language === 'en'">{{ group.labelEn }}</span>

      <el-button-group v-if="renderMode === 'design'" class="group-title-btns">
        <el-button
            :icon="Delete"
            type="danger"
            circle
            size="small"
            @click.stop="deleteGroup"
            title="删除"
        />
        <el-button
            :icon="ArrowUp"
            circle
            size="small"
            :disabled="groupIdx === 0"
            @click.stop="moveGroupUp"
            title="上移"
            class="sort-btn"
        />
        <el-button
            :icon="ArrowDown"
            circle
            size="small"
            :disabled="groupIdx === groupCount - 1"
            @click.stop="moveGroupDown"
            title="下移"
            class="sort-btn"
        />
        <el-button
            :icon="Edit"
            type="primary"
            circle
            size="small"
            @click.stop="openGroupEditDialog"
            title="编辑分组"
        />
      </el-button-group>
    </div>
    <el-row :gutter="16" class="group-fields">
      <template v-if="fields.length === 0">
        <el-col :span="24">
          <div v-if="renderMode === 'design'" class="empty-tip">
            请拖拽控件到此区域
          </div>
        </el-col>
      </template>
      <template v-else>
        <template v-for="(item, idx) in processedFields" :key="item._key">
          <el-col v-if="item._empty" :span="item._span"></el-col>
          <el-col v-else :span="item.unit * 2">
            <div class="field-item-wrapper">
              <FieldItem
                  :field="item"
                  :unit="item.unit"
                  :render-mode="renderMode"
                  :index="item._originIndex"
                  :total="fields.length"
                  :language="language"
                  :formData="formData"
                  @delete-field="deleteField"
                  @edit-field="editField"
                  @move-up="({ index }) => moveFieldUp(fields, index)"
                  @move-down="({ index }) => moveFieldDown(fields, index)"
                  @update-value="onUpdateValue"
                  @update:field="onUpdateField"
              />
            </div>
          </el-col>
        </template>
      </template>
    </el-row>
    <!-- 不再需要 FieldEditDialog，由 BaseField 组件处理 -->
    <el-dialog
        v-model="groupEditDialogVisible"
        :title="groupEditDialogTitle"
        width="400px"
        :append-to-body="true"
        :close-on-click-modal="false"
        :show-close="true"
        destroy-on-close
        style="z-index: 3000 !important"
        class="group-edit-dialog"
    >
      <div class="dialog-body-scroll">
        <el-form :model="groupEditForm" label-width="70px">
          <el-form-item label="中文标签" required>
            <el-input
                v-model="groupEditForm.labelZh"
                placeholder="请输入中文标签"
            />
          </el-form-item>
          <el-form-item label="英文标签">
            <el-input
                v-model="groupEditForm.labelEn"
                placeholder="请输入英文标签"
            />
          </el-form-item>
          <el-form-item label="编码" required>
            <el-input v-model="groupEditForm.code" placeholder="请输入编码"/>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelGroupEdit">取消</el-button>
          <el-button type="primary" @click="saveGroupEdit">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {nanoid} from "nanoid";
import {defineProps, defineEmits, computed, ref, nextTick} from "vue";
import FieldItem from "./FieldItem.vue";
import {fieldTypeMap} from "./form-fields";
import {
  ElButton,
  ElButtonGroup,
  ElInput,
  ElDialog,
  ElForm,
  ElFormItem,
  ElMessage,
  ElRow,
  ElCol,
} from "element-plus";
import {ArrowUp, ArrowDown, Delete, Edit} from "@element-plus/icons-vue";
import {type RenderMode} from "./form-fields/index";
import {processFieldsLayout} from "./form-fields/utils/processFieldsLayout";

const props = defineProps({
  group: {
    type: Object,
    required: true,
  },
  fields: {
    type: Array,
    required: true,
  },
  groupIdx: {
    type: Number,
    default: undefined,
  },
  groupCount: {
    type: Number,
    default: 0,
  },
  renderMode: {
    type: String as () => RenderMode,
    default: "design",
  },
  language: {
    type: String,
    default: "zh",
  },
  groups: {
    type: Array,
    required: true,
  },
  formData: {
    type: Object as PropType<Record<string, any>>,
    required: true,
  },
});

const emit = defineEmits([
  "dropField",
  "update:field",
  "deleteGroup",
  "moveGroup",
  "deleteField",
  "moveField",
  "update-value"
]);

// 添加调试日志，记录组件的初始化
console.log("[DEBUG] GroupBlock 组件初始化，props:", props);

// 使用组件级别的状态，不再使用全局状态
const groupEditDialogVisible = ref(false);
const groupEditForm = ref({code: "", labelZh: "", labelEn: ""});
const groupEditDialogTitle = ref("");
const groupEditTarget = ref<any>(null);

function openGroupEditDialog() {
  // 如果分组没有code，生成一个默认的code
  if (!props.group.code) {
    // 生成唯一ID
    const uniqueId = Math.random().toString(36).slice(2);
    // 设置默认编码
    props.group.code = `grp_${uniqueId.substring(0, 6)}`;
  }

  // 确保表单数据正确初始化
  groupEditForm.value = {
    code: props.group.code || "",
    labelZh: props.group.labelZh || "",
    labelEn: props.group.labelEn || "",
  };

  // 设置对话框标题
  groupEditDialogTitle.value = "编辑分组";

  // 设置编辑目标
  groupEditTarget.value = props.group;

  // 显示对话框 - 使用 nextTick 确保在 DOM 更新后再显示对话框
  nextTick(() => {
    groupEditDialogVisible.value = true;

    console.log("打开编辑对话框", {
      title: groupEditDialogTitle.value,
      visible: groupEditDialogVisible.value,
      form: groupEditForm.value,
      target: groupEditTarget.value,
    });
  });
}

// 取消编辑
function cancelGroupEdit() {
  console.log("取消编辑");
  groupEditDialogVisible.value = false;
}

// 保存编辑
function saveGroupEdit() {
  console.log("[DEBUG] 保存编辑开始，表单数据:", groupEditForm.value);
  console.log("[DEBUG] 当前编辑目标:", groupEditTarget.value);

  if (!groupEditForm.value.labelZh.trim()) {
    ElMessage.error("中文不能为空");
    return;
  }

  // 如果编码为空，生成一个默认编码
  if (!groupEditForm.value.code.trim()) {
    // 生成唯一ID
    const uniqueId = Math.random().toString(36).slice(2);
    // 设置默认编码
    groupEditForm.value.code = `grp_${uniqueId.substring(0, 6)}`;
  }

  if (groupEditTarget.value) {
    // 保存修改前的值，用于比较
    // const oldName = groupEditTarget.value.name;
    const oldCode = groupEditTarget.value.code;

    // 更新值
    // groupEditTarget.value.name = groupEditForm.value.name.trim();
    groupEditTarget.value.code = groupEditForm.value.code.trim();
    groupEditTarget.value.labelZh = groupEditForm.value.labelZh.trim();
    groupEditTarget.value.labelEn = groupEditForm.value.labelEn.trim();

    // console.log("[DEBUG] 名称变更:", oldName, "->", groupEditTarget.value.name);
    console.log("[DEBUG] 编码变更:", oldCode, "->", groupEditTarget.value.code);
    console.log("[DEBUG] 中文标签变更:", groupEditTarget.value.labelZh);
    console.log("[DEBUG] 英文标签变更:", groupEditTarget.value.labelEn);

    // 触发更新事件，确保父组件知道数据已更改
    console.log("[DEBUG] 触发根分组更新事件:", {
      id: groupEditTarget.value.id,
      type: "group",
      updated: true,
    });
    emit("update:field", {
      id: groupEditTarget.value.id,
      type: "group",
      updated: true,
    });

    console.log("[DEBUG] 保存成功，更新后的目标:", groupEditTarget.value);

    // 强制刷新
    nextTick(() => {
      console.log("[DEBUG] 强制刷新完成");
    });
  }

  groupEditDialogVisible.value = false;
}

// 创建新控件
function createField(type: string) {
  // 公共字段
  let field: any = {
    id: nanoid(),
    code: `field_${nanoid(6)}`,
    type,
    newLine: false,
    labelZh: "",
    labelEn: "",
    required: false,
    unit: 6,
  };
  // 合并特有属性
  if (fieldTypeMap[type]?.getDefaultProps) {
    Object.assign(field, fieldTypeMap[type].getDefaultProps());
  }

  return field;
}

function onDropInsert(e: DragEvent) {
  console.log("[DEBUG] onDropInsert 被调用");
  const type = e.dataTransfer?.getData("fieldType");
  console.log("[DEBUG] 拖拽的控件类型:", type);

  const field = createField(type);
  props.fields.push(field);

  // if (
  //   type === "group" ||
  //   (typeof window !== "undefined" && (window as any).__draggingGroup)
  // ) {
  //   // 直接在 groups 的 idx 位置插入新分组
  //   const uniqueId = Date.now() + "-" + Math.floor(Math.random() * 10000);
  //   const defaultCode = `grp_${uniqueId.toString()}`;
  //   const newGroup = {
  //     id: "g" + uniqueId,
  //     labelZh: `分组${props.groups.length + 1}`,
  //     labelEn: `Group${props.groups.length + 1}`,
  //     type: "group",
  //     code: defaultCode,
  //     fields: [],
  //   };
  //   props.groups.splice(idx, 0, newGroup);
  //   emit("update:field", { id: newGroup.id, updated: true });
  // }
}

// 控件属性编辑处理函数
function editField(field: any) {
  // 查找并更新字段
  const findField = (fields: any[], fieldId: string): any => {
    for (let i = 0; i < fields.length; i++) {
      if (fields[i].id === field.id) {
        Object.assign(fields[i], field);
        return true;
      }
      if (
          (fields[i].type === "subForm" || fields[i].type === "multiSubForm") &&
          fields[i].fields
      ) {
        if (findField(fields[i].fields, field.id)) return true;
      }
    }
    return false;
  };
  findField(props.fields, field.id);
  emit("update:field", {id: props.group.id, updated: true});
}

// 拖拽排序相关函数已移除，因为我们现在使用 FieldItem 组件来处理字段的渲染和事件

// 拖拽高亮
const dragOver = ref(false);

function onDragOver(e: DragEvent) {
  // 拖拽分组时不高亮分组本身
  const type = e.dataTransfer?.getData("fieldType");
  if (
      type === "group" ||
      (typeof window !== "undefined" && (window as any).__draggingGroup)
  )
    return;
  dragOver.value = true;
}

function onDragLeave() {
  dragOver.value = false;
}

function onDrop(e: DragEvent) {
  e.stopPropagation();
  dragOver.value = false;
  let type = e.dataTransfer?.getData("fieldType");
  if (
      !type &&
      typeof window !== "undefined" &&
      (window as any).__dragFieldType
  ) {
    type = (window as any).__dragFieldType;
    delete (window as any).__dragFieldType;
  }
  // 拖拽分组时不处理
  if (
      type === "group" ||
      (typeof window !== "undefined" && (window as any).__draggingGroup)
  )
    return;

  if (type) {
    emit("dropField", {type, groupId: props.group.id});
    return;
  }
  // 只有没有 fieldType 时才处理分组排序
  const fromIdx = e.dataTransfer?.getData("groupIdx");
  if (fromIdx && String(props.groupIdx) !== fromIdx) {
    emit("moveGroup", {fromIdx: Number(fromIdx), toIdx: props.groupIdx});
    return;
  }
}

const groupCount = computed(() => props.groupCount);

function moveGroupUp() {
  if (typeof props.groupIdx === "number" && props.groupIdx > 0) {
    // 交换 groups[groupIdx] 和 groups[groupIdx-1]
    const idx = props.groupIdx;
    const temp = props.groups[idx - 1];
    props.groups[idx - 1] = props.groups[idx];
    props.groups[idx] = temp;
    emit("update:field", {id: props.group.id, updated: true});
  }
}

function moveGroupDown() {
  if (
      typeof props.groupIdx === "number" &&
      props.groupIdx < props.groups.length - 1
  ) {
    // 交换 groups[groupIdx] 和 groups[groupIdx+1]
    const idx = props.groupIdx;
    const temp = props.groups[idx + 1];
    props.groups[idx + 1] = props.groups[idx];
    props.groups[idx] = temp;
    emit("update:field", {id: props.group.id, updated: true});
  }
}

function moveFieldUp(fields: any[], fieldIdx: number) {
  if (fieldIdx > 0) {
    const temp = fields[fieldIdx - 1];
    fields[fieldIdx - 1] = fields[fieldIdx];
    fields[fieldIdx] = temp;
    emit("update:field", {id: props.group.id, updated: true});
  }
}

function moveFieldDown(fields: any[], fieldIdx: number) {
  if (fieldIdx < fields.length - 1) {
    const temp = fields[fieldIdx + 1];
    fields[fieldIdx + 1] = fields[fieldIdx];
    fields[fieldIdx] = temp;
    emit("update:field", {id: props.group.id, updated: true});
  }
}

// 删除控件
function deleteField(fieldId: string) {
  const fieldIndex = props.fields.findIndex((f: any) => f.id === fieldId);
  if (fieldIndex !== -1) {
    props.fields.splice(fieldIndex, 1);
    emit("update:field", {id: props.group.id, updated: true});
  }
}

// 子表单相关函数已移除，因为子表单现在是普通控件

const processedFields = computed(() => processFieldsLayout(props.fields || []));

function deleteGroup() {
  console.log("[DEBUG] deleteGroup 被调用，groupId:", props.group.id);
  console.log("[DEBUG] 当前分组所在的 groups 数组:", props.groups);
  console.log("[DEBUG] 当前组件的 props:", props);

  // 查找要删除的分组在当前分组数组中的索引
  const groupIndex = props.groups.findIndex(
      (g: any) => g.id === props.group.id
  );

  if (groupIndex !== -1) {
    console.log("[DEBUG] 在当前组件中找到要删除的分组，直接删除");
    // 如果分组在当前组件中，直接从数组中删除
    props.groups.splice(groupIndex, 1);
    // 通知父组件分组已更新
    emit("update:field", {id: props.group.id, updated: true});
  } else {
    console.log("[DEBUG] 分组不在当前组件中，通过事件向上传递");
    // 如果分组不在当前组件中，通过事件向上传递
    emit("deleteGroup", props.group.id);
  }
}

function onUpdateValue(field: any) {


  // 只更新 value，不 emit 事件，避免递归
  const fields = props.fields as any[];
  const idx = fields.findIndex((f: any) => f.id === field.id);
  if (idx !== -1) {
    fields[idx].value = field.value;
    // 不要 emit("update:field", ...)，否则会递归
  }

  emit("update-value", field);
}

function onUpdateField(field: any) {
  // 找到对应 field，写回 value
  const fields = props.fields as any[];
  const idx = fields.findIndex((f: any) => f.id === field.id);
  if (idx !== -1) {
    fields[idx].value = field.value;
    emit("update:field", {id: props.group.id, updated: true});
  }
}
</script>

<style scoped>
.group-block {
  margin-bottom: 32px;
  border: 1px solid #eee;
  border-radius: 8px;
  background: #fff;
  padding: 18px 20px 18px 20px;
  transition: box-shadow 0.2s, border-color 0.2s;
  width: 100%;
}

.group-block.drag-over {
  border-color: #409eff;
  box-shadow: 0 0 0 2px #409eff33;
}

.group-title {
  font-weight: bold;
  margin-bottom: 16px;
  font-size: 17px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

/* 子表单相关样式已移除 */
.empty-tip {
  color: #aaa;
  background: #f8f8fa;
  border: 1px dashed #ddd;
  border-radius: 4px;
  padding: 24px;
  text-align: center;
  margin-bottom: 12px;
}

.group-title-input {
  font-size: 16px;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #ccc;
  width: 120px;
  margin-right: 8px;
}

.field-item-wrapper {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  gap: 4px;
  flex: 1;
  position: relative;

  /* border: 1px solid #dcdfe6; */
  padding: 10px;
  margin-bottom: 8px;
  border-radius: 4px;
}

.field-action-btns {
  display: flex;
  justify-content: flex-end;
  margin-top: 8px;
  margin-bottom: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  gap: 10px;
}

.dialog-body-scroll {
  max-height: 60vh;
  overflow-y: auto;
  padding: 10px 0;
}

.group-edit-dialog :deep(.el-dialog__body) {
  padding: 10px 20px;
}
</style>
