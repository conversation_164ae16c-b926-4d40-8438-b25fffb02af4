<template>
  <el-card
      class="field-item-card"
      shadow="never"
      body-style="padding: 0; background: none;"
  >
    <component
        :is="fieldComponent"
        :field="props.field"
        :value="props.field.value"
        :annotation="props.field.annotations"
        :previous-value="props.field.previousValue"
        :render-mode="renderMode"
        :language="language"
        :formData="formData"
        :index="index"
        :total="total"
        @update:value="onUpdateValue"
        @update:field="onUpdateField"
        @delete-field="onDeleteField"
        @edit-field="onEditField"
        @move-up="onMoveUp"
        @move-down="onMoveDown"
        ref="fieldRef"
    />
  </el-card>
</template>

<script setup lang="ts">
import {defineProps, defineEmits, computed, watch, ref} from "vue";
import {fieldTypeMap} from "./form-fields/index";
import {ElCard} from "element-plus";
import {type RenderMode} from "./form-fields/index";

const props = defineProps({
  field: {
    type: Object,
    required: true,
  },
  unit: {
    type: Number,
    default: 1,
  },
  renderMode: {
    type: String as () => RenderMode,
    default: "edit",
  },
  index: {
    type: Number,
  },
  total: {
    type: Number,
  },
  language: {
    type: String,
    default: "zh",
  },
  formData: {
    type: Object as PropType<Record<string, any>>,
    required: true,
  },
  rowIndex: {
    type: Number,
  },
});

const emit = defineEmits([
  "update:field",
  "delete-field",
  "edit-field",
  "move-up",
  "move-down",
  "update-value",
]);

const fieldRef = ref();

const fieldComponent = computed(() => {
  return (
      fieldTypeMap[props.field.type]?.component ||
      fieldTypeMap["input"]?.component
  );
});

// 处理字段事件 - 保持事件转发，但不再 emit 到更上层
function onDeleteField(fieldId: string) {
  // 直接传递删除字段事件
  emit("delete-field", fieldId);
}

function onEditField(field: any) {
  // 记录日志并直接传递编辑字段事件
  console.log("[DEBUG] FieldItem 接收到 edit-field 事件，field:", field);
  emit("edit-field", field);
}

function onMoveUp(data: any) {
  // 直接传递上移事件
  emit("move-up", data);
}

function onMoveDown(data: any) {
  // 直接传递下移事件
  emit("move-down", data);
}

function onUpdateValue(val: unknown) {
  // 只更新 field.value，不向上传递
  emit("update-value", {...props.field, value: val});
}

function onUpdateField(field: any) {
  console.log("[DEBUG] FieldItem 接收到 update:field 事件，field:", field);
  // 直接向上传递
  emit("update:field", field);
}
</script>

<style scoped>
.field-item-card {
  margin-bottom: 0;
  background: none;
  box-shadow: none;
  border: none;
}
</style>
