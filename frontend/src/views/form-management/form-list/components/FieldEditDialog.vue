<template>
  <el-dialog
      :model-value="visible"
      @update:model-value="emit('cancel')"
      title="编辑控件属性"
      width="800px"
      :close-on-click-modal="false"
      :show-close="true"
      append-to-body
      style="z-index: 3000 !important"
      destroy-on-close
      class="field-edit-dialog"
  >
    <el-form label-width="200px" class="edit-form" label-position="right">
      <!-- 通用属性部分 -->
      <el-form-item label="编码">
        <el-input v-model="editField.code" placeholder="请输入控件编码"/>
      </el-form-item>

      <el-form-item label="中文标签">
        <el-input v-model="editField.labelZh" placeholder="请输入中文标签"/>
      </el-form-item>
      <el-form-item label="英文标签">
        <el-input v-model="editField.labelEn" placeholder="请输入英文标签"/>
      </el-form-item>
      <el-form-item label="必填">
        <el-checkbox v-model="editField.required"/>
      </el-form-item>
      <el-form-item label="新行">
        <el-checkbox v-model="editField.newLine"/>
      </el-form-item>
      <el-form-item label="所占单位">
        <div class="unit-input-container">
          <el-button
              type="default"
              :disabled="editField.unit <= 0"
              @click="decreaseUnit"
              class="unit-btn"
          >-
          </el-button
          >
          <el-input-number
              v-model="editField.unit"
              :min="1"
              :max="12"
              :controls="false"
              class="unit-input"
          />
          <el-button
              type="default"
              :disabled="editField.unit >= 12"
              @click="increaseUnit"
              class="unit-btn"
          >+
          </el-button
          >
        </div>
      </el-form-item>

      <el-form-item label="内部控件宽度">
        <div class="unit-input-container">
          <el-button
              type="default"
              :disabled="editField.extends.width <= 0"
              @click="decreaseWidth"
              class="unit-btn"
          >-
          </el-button
          >
          <el-input-number
              v-model="editField.extends.width"
              :min="1"
              :max="12"
              :controls="false"
              class="unit-input"
          />
          <el-button
              type="default"
              :disabled="editField.extends.width >= 12"
              @click="increaseWidth"
              class="unit-btn"
          >+
          </el-button
          >
        </div>
      </el-form-item>
      <el-form-item label="是否有ToolTip底部">
        <el-checkbox v-model="hasToolTip"/>

      </el-form-item>
      <el-form-item label="底部ToolTip样式" v-if="hasToolTip&&editField.description&&editField.description.tooltip">
        <el-select v-model="editField.description.tooltip.type">
          <el-option label="危险" value="danger"/>
          <el-option label="警告" value="warning"/>
          <el-option label="强调" value="primary"/>
          <el-option label="一般" value="info"/>
        </el-select>
      </el-form-item>
      <el-form-item label="底部ToolTip是否分行显示"
                    v-if="hasToolTip&&editField.description&&editField.description.tooltip">
        <el-checkbox v-model="editField.description.tooltip.split"/>
      </el-form-item>
      <el-form-item label="底部ToolTip中文" v-if="hasToolTip&&editField.description&&editField.description.tooltip">
        <el-input type="textarea" v-model="editField.description.tooltip.zh"/>
      </el-form-item>

      <el-form-item label="底部ToolTip英文" v-if="hasToolTip&&editField.description&&editField.description.tooltip">
        <el-input type="textarea" v-model="editField.description.tooltip.en"/>
      </el-form-item>


      <el-form-item label="是否有右侧ToolTip">
        <el-checkbox v-model="hasRightToolTip"/>

      </el-form-item>
      <el-form-item label="右侧ToolTip样式" v-if="hasRightToolTip&&editField.description&&editField.description.right">
        <el-select v-model="editField.description.right.type">
          <el-option label="危险" value="danger"/>
          <el-option label="警告" value="warning"/>
          <el-option label="强调" value="primary"/>
          <el-option label="一般" value="info"/>
        </el-select>
      </el-form-item>
      <el-form-item
          label="右侧ToolTip是否分行显示"
          v-if="hasRightToolTip&&editField.description&&editField.description.right">
        <el-checkbox v-model="editField.description.right.split"/>
      </el-form-item>
      <el-form-item label="右侧ToolTip中文" v-if="hasRightToolTip&&editField.description&&editField.description.right">
        <el-input type="textarea" v-model="editField.description.right.zh"/>
      </el-form-item>

      <el-form-item label="右侧ToolTip英文" v-if="hasRightToolTip&&editField.description&&editField.description.right">
        <el-input type="textarea" v-model="editField.description.right.en"/>
      </el-form-item>

      <!-- 动态加载字段类型特有的属性编辑器 -->
      <component
          :is="propertyEditor"
          :field="editField"
          @change="onPropertyChange"
      />
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="onCancel">取消</el-button>
        <el-button type="primary" @click="save">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import {ref, watch, computed} from "vue";
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElInputNumber,
  ElCheckbox,
  ElButton,
} from "element-plus";
import {getPropertyEditor} from "./field-property-editors/index";

const props = defineProps({
  visible: Boolean,
  field: Object,
  fieldTypes: Array,
});
const emit = defineEmits(["save", "cancel"]);

const editField = ref<any>({});

// 根据字段类型获取对应的属性编辑器组件
const propertyEditor = computed(() => {
  if (!editField.value || !editField.value.type) return null;
  return getPropertyEditor(editField.value.type);
});

const hasToolTip = ref(false);
const hasRightToolTip = ref(false)
watch(
    () => props.visible,
    (val) => {
      if (val && props.field) {
        // 深拷贝字段对象
        editField.value = JSON.parse(JSON.stringify(props.field));

        // 确保extends属性存在
        if (!editField.value.extends) {
          editField.value.extends = {};
        }

        // 确保code属性存在
        if (!editField.value.code) {
          editField.value.code = `field_${Date.now().toString(36)}`;
        }

        hasToolTip.value = editField.value.description != null && editField.value.description.tooltip != null;
        hasRightToolTip.value = editField.value.description != null && editField.value.description.right != null;

      }
    }
);



watch(hasToolTip, (val) => {
  if (!editField.value.description) {
    editField.value.description = {};
  }
  if (val && !editField.value.description.tooltip) {
    editField.value.description.tooltip = {type: 'info', zh: '', en: '', split: false};
  }
});


watch(hasRightToolTip, (val) => {
  if (!editField.value.description) {
    editField.value.description = {};
  }
  if (val && !editField.value.description.right) {
    editField.value.description.right = {type: 'info', zh: '', en: '', split: false};
  }
});

// 增加单位值
function increaseUnit() {
  if (editField.value.unit < 12) {
    editField.value.unit++;
  }
}

// 减少单位值
function decreaseUnit() {
  if (editField.value.unit > 0) {
    editField.value.unit--;
  }
}

// 增加单位值
function increaseWidth() {
  if (editField.value.extends.width < 12) {
    editField.value.extends.width++;
  }
}

// 减少单位值
function decreaseWidth() {
  if (editField.value.extends.width > 0) {
    editField.value.extends.width--;
  }
}

// 处理属性编辑器的变更事件
function onPropertyChange(field: any) {
  // 属性编辑器可能会直接修改 editField 对象，这里不需要额外处理
  console.log("[DEBUG] 属性已更新:", field);
}

// 保存
function save() {
  editField.value.description ??= {};
  if (editField.value.description) {
    if (!hasToolTip.value) {
      editField.value.description.tooltip = null;
    }
    if (!hasRightToolTip.value) {
      editField.value.description.right = null;
    }
  }


  emit("save", JSON.parse(JSON.stringify(editField.value)));
}

// 取消
function onCancel() {
  emit("cancel");
}
</script>

<style scoped>
.field-edit-dialog :deep(.el-dialog__header) {
  padding: 16px 20px;
  margin-right: 0;
  border-bottom: 1px solid #ebeef5;
  background-color: #f5f7fa;
}

.field-edit-dialog :deep(.el-dialog__title) {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.field-edit-dialog :deep(.el-dialog__footer) {
  padding: 12px 20px;
  border-top: 1px solid #ebeef5;
  background-color: #f5f7fa;
}

.edit-form {
  padding-top: 8px;
}

.edit-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #333;
}

.edit-form :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
}

.edit-form :deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #c0c4cc inset;
}

.edit-form :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409eff inset;
}

/* 单位输入框样式 */
.unit-input-container {
  display: flex;
  align-items: center;
  width: 100%;
}

.unit-btn {
  padding: 8px 12px;
  font-weight: bold;
}

.unit-input {
  margin: 0 8px;
  width: 100%;
}

.unit-input :deep(.el-input__wrapper) {
  text-align: center;
}

.unit-input :deep(.el-input__inner) {
  text-align: center;
}

/* 选项相关样式已移至 OptionsPropertyEditor.vue */

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
