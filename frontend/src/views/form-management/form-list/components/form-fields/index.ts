// Import field components
import Input<PERSON>ield from './InputField'
import Textarea<PERSON>ield from './TextareaField'
import TextM<PERSON>ilang<PERSON>ield from './TextMultilangField'
import TextareaMultilangField from './TextareaMultilangField'
import SelectField from './SelectField'
import CheckboxField from './CheckboxField'
import RadioField from './RadioField'
import DateField from './DateField'
import NumberField from './NumberField'
import UnitSelectField from './UnitSelectField'
import IntRangeField from './IntRangeField'
import DateRangeField from './DateRangeField'
import SubFormField from './SubFormField'
import MultiSubFormField from './MultiSubFormField'
import FileField from './FileField'

export const fieldTypeMap: Record<string, any> = {
  input: InputField,
  textarea: TextareaField,
  text_multilang: TextMultilangField,
  textarea_multilang: TextareaMultilangField,
  select: SelectField,
  checkbox: CheckboxField,
  radio: RadioField,
  date: DateField,
  number: NumberField,
  unit_select: UnitSelectField,
  int_range: IntRangeField,
  date_range: DateRangeField,
  subForm: SubFormField,
  multiSubForm: MultiSubFormField,
  file: FileField
}

// Export the base field components for direct use
export { default as BaseField, type RenderMode } from './BaseField.vue'
export { default as BaseSubFormField } from './BaseSubFormField.vue'