// 示例：单位选择字段的校验逻辑
export function validate(value: any, field: any, language: string) {
    if (value.unit != null && value.unit !== "" && !field.options?.some(opt => opt.value === value.unit)) {
        return (language == 'both' || language == 'zh') ? "无效选项" : 'Invalid option';
    }
    if (field.required && (value.value == null || value.value === "")) {
        return (language == 'both' || language == 'zh') ? "必填" : "Required";
    }
    if (field.required && (value.unit == null || value.unit === "")) {
        return (language == 'both' || language == 'zh') ? "必选" : "Required";
    }
    return '';
} 