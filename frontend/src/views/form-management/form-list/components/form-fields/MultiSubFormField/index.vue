<template>
  <div ref="selfRef">
    <BaseSubFormField
        ref="baseSubForm"
        :field="field"
        :value="value"
        :previousValue="previousValue"
        :language="language"
        :renderMode="renderMode"
        :formData="formData"
        :index="index"
        :total="total"
        default-label="多行子表单"
        :multi-row="true"
        @update:value="$emit('update:value', $event)"
        @update:field="$emit('update:field', $event)"
        @delete-field="$emit('delete-field', $event)"
        @move-up="$emit('move-up', $event)"
        @move-down="$emit('move-down', $event)"

        @edit-field="(...args) => $emit('edit-field', ...args)"
    >
      <div
          class="multi-subform-container"
          :class="{ 'design-mode': renderMode === 'design' }"
          @dragover.prevent="baseSubForm?.onDragOver"
          @dragleave="baseSubForm?.onDragLeave"
          @drop="baseSubForm?.onDrop"
      >
        <!-- 子表单内容区域 -->
        <div class="multi-subform-fields">
          <!-- 设计模式下的字段定义区域 -->
          <div v-if="renderMode === 'design'" class="subform-definition">
            <div class="subform-definition-header">
              <h4>字段定义</h4>
            </div>
            <div class="subform-definition-content">
              <template v-if="field.fields && field.fields.length > 0">
                <el-row :gutter="16">
                  <template v-for="item in processedFields" :key="item._key">
                    <el-col v-if="item._empty" :span="item._span"></el-col>
                    <el-col v-else :span="item.unit * 2">
                      <div class="field-content">
                        <div class="subform-field-row">
                          <component
                              :is="baseSubForm?.getFieldComponent(item.type)"
                              :field="item"
                              :in-subform="true"
                              :value="item.value"
                              :render-mode="renderMode"
                              :formData="formData"
                              :index="item._originIndex"
                              :total="processedFields.length"
                              :language="language"
                              @update:value="(val: any) => updateSubFieldValue(item._originIndex, val)"
                              @delete-field="onDeleteSubField"
                              @edit-field="baseSubForm?.onEditSubField"
                              @move-up="onMoveSubFieldUp"
                              @move-down="onMoveSubFieldDown"
                          />
                        </div>
                      </div>
                    </el-col>
                  </template>
                </el-row>
              </template>
              <div
                  v-else
                  class="empty-subform"
                  @dragover.prevent="baseSubForm?.onDragOver"
                  @dragleave="baseSubForm?.onDragLeave"
                  @drop="baseSubForm?.onDrop"
              >
                <span>请拖拽控件到此子表单</span>
              </div>
            </div>
          </div>

          <!-- 非设计模式下的多行表单区域 -->
          <template v-else>
            <template v-if="rows.length > 0">
              <div
                  v-for="(row, rowIndex) in rows"
                  :key="rowIndex"
                  :class="{
                  'multi-subform-instance': true,
                  'approval-mode': renderMode === 'approval',
                  'has-warning':
                    field.annotations &&
                    field.annotations[rowIndex] &&
                    field.annotations[rowIndex].approval,
                }"
              >
                <div v-if="renderMode === 'approval'" class="field-actions">
                  <el-button-group>
                    <el-button
                        :icon="Comment"
                        circle
                        size="small"
                        @click.stop="onApprovalMark(rowIndex)"
                        title="批注"
                    />
                    <el-button
                        v-if="
                        field.annotations != null &&
                        field.annotations[rowIndex] != null &&
                        field.annotations[rowIndex].approval != null &&
                        field.annotations[rowIndex].approval.length > 0
                      "
                        :icon="Remove"
                        circle
                        size="small"
                        @click.stop="onRemove(rowIndex)"
                        title="取消"
                    />
                  </el-button-group>
                </div>
                <div class="multi-subform-instance-header">
                  <h4>#{{ rowIndex + 1 }}</h4>

                  <el-text type="primary" style="flex: 1; margin-left: 5px">{{
                      field.annotations &&
                      field.annotations[rowIndex] != null &&
                      field.annotations[rowIndex].fill != null &&
                      field.annotations[rowIndex].fill.length > 0
                          ? "⬇️AI提取内容：" + field.annotations[rowIndex]?.fill
                          : ""
                    }}
                  </el-text>
                  <el-text type="danger" style="flex: 1; margin-left: 5px">
                    {{
                      field.annotations &&
                      field.annotations[rowIndex] != null &&
                      field.annotations[rowIndex].approval != null &&
                      field.annotations[rowIndex].approval.length > 0
                          ? "驳回原因：" + field.annotations[rowIndex]?.approval
                          : ""
                    }}
                  </el-text
                  >
                  <div
                      v-if="
                      (renderMode === 'edit' || renderMode === 'add') &&
                      !hasFillAnnotations(rowIndex)
                    "
                      class="multi-subform-instance-actions"
                  >
                    <el-button-group>
                      <el-button
                          :icon="ArrowUp"
                          circle
                          size="small"
                          :disabled="rowIndex === 0"
                          @click="moveRowUp(rowIndex)"
                          :title="language === 'both' ? '上移/Move up' : (language === 'zh' ? '上移' : 'Move up')"
                      />
                      <el-button
                          :icon="ArrowDown"
                          circle
                          size="small"
                          :disabled="rowIndex === rows.length - 1"
                          @click="moveRowDown(rowIndex)"
                          :title="language === 'both' ? '下移/Move down' : (language === 'zh' ? '下移' : 'Move down')"
                      />
                      <el-button
                          :icon="Delete"
                          type="danger"
                          circle
                          size="small"
                          @click="deleteRow(rowIndex)"
                          :title="language === 'both' ? '删除/Delete' : (language === 'zh' ? '删除' : 'Delete')"
                      />
                    </el-button-group>
                  </div>
                </div>
                <div class="multi-subform-instance-content">
                  <el-row :gutter="16">
                    <template v-for="item in processedFields" :key="item._key">
                      <el-col v-if="item._empty" :span="item._span"></el-col>
                      <el-col v-else :span="item.unit * 2">
                        <div class="subform-field-row">
                          <component
                              :is="baseSubForm?.getFieldComponent(item.type)"
                              :field="item"
                              :in-subform="true"
                              :value="row[item.code]"
                              :language="language"
                              :formData="formData"
                              :rowIndex="rowIndex"
                              :annotation="
                              item.annotations && item.annotations[rowIndex]
                                ? item.annotations[rowIndex]
                                : null
                            "
                              :previous-value="
                              previousRows.length - 1 >= rowIndex &&
                              previousRows[rowIndex] != null
                                ? previousRows[rowIndex][item.code]
                                : null
                            "
                              :render-mode="renderMode"
                              @update:value="(val: any) => updateCellValue(rowIndex, item.code, val)"
                          />
                        </div>
                      </el-col>
                    </template>
                  </el-row>
                </div>
              </div>
            </template>
            <div v-else class="empty-multi-subform">
              <span>暂无数据</span>
            </div>
          </template>
        </div>

        <!-- 操作按钮 - 只在非设计模式下显示 -->
        <div
            v-if="
            (renderMode === 'edit' || renderMode === 'add') &&
            field.fields &&
            field.fields.length > 0
          "
            class="multi-subform-actions"
        >
          <el-button type="success" size="small" @click="addRow" v-if="language=='both'">
            添加行/Add row
          </el-button>
          <el-button type="success" size="small" @click="addRow" v-if="language=='zh'">
            添加行
          </el-button>
          <el-button type="success" size="small" @click="addRow" v-if="language=='en'">
            Add row
          </el-button>
        </div>
      </div>
    </BaseSubFormField>
    <FieldAnnotationDialog
        :visible="annotationDialogVisible"
        :field="annotationField"
        :annotation="annotations ?? {}"
        @save="onSaveAnnotationField"
        @cancel="annotationDialogVisible = false"
    />
  </div>
</template>

<script setup lang="ts">
import {ElButton, ElButtonGroup} from "element-plus";
import {ArrowUp, ArrowDown, Delete} from "@element-plus/icons-vue";
import BaseSubFormField from "../BaseSubFormField.vue";
import {BaseField, type RenderMode} from "../index";
import {processFieldsLayout} from "../utils/processFieldsLayout";
import {Comment, Remove} from "@element-plus/icons-vue";
import FieldAnnotationDialog from "../../FieldAnnotationDialog.vue";
import {ref, watch, defineExpose, computed, onMounted, onUnmounted, getCurrentInstance, inject} from "vue";
import {validate as logicValidate} from "./logic"; // 你的校验逻辑


interface MultiSubFormValue {
  rows: Array<Record<string, any>>;
}

const annotationDialogVisible = ref(false);
const annotations = ref(null);
const annotationField = ref<any>(null);
const annotationRowIndex = ref<number>(-1);

const props = defineProps({
  field: {type: Object, required: true},
  value: {type: Array as () => Array<Record<string, any>>, default: () => []},
  previousValue: {
    type: Array as () => Array<Record<string, any>>,
    default: () => [],
  },
  formData: {
    type: Object as PropType<Record<string, any>>,
    required: true,
  },
  annotation: {type: Object, required: false},
  renderMode: {type: String as () => RenderMode, default: "edit"},
  index: {type: Number},
  total: {type: Number},
  language: {type: String as () => "zh" | "en" | "both", default: "zh"},
  setFieldRef: {type: Function, default: null},
});

const emit = defineEmits([
  "update:value",
  "update:field",
  "delete-field",
  "edit-field",
  "move-up",
  "move-down",
]);

// 审批批注
function onApprovalMark(rowIndex: number) {
  annotationField.value = JSON.parse(JSON.stringify(props.field));
  annotations.value =
      props.field.annotations != null &&
      props.field.annotations.length >= rowIndex
          ? props.field.annotations[rowIndex]
          : null;

  annotationDialogVisible.value = true;
  annotationRowIndex.value = rowIndex;
}

async function onRemove(rowIndex) {
  await ElMessageBox.confirm(`是否确认进行该操作？/nAre you sure you want to do this?`);
  props.field.annotations[rowIndex].approval = null;
  onSaveAnnotationField(props.field.annotations[rowIndex]);
}

function onSaveAnnotationField(annotation: any) {
  annotationDialogVisible.value = false;

  if (props.field.annotations == null) {
    props.field.annotations = [];
  }

  props.field.value.forEach((row, index) => {
    if (props.field.annotations.length - 1 < index) {
      props.field.annotations.splice(index, 0, {approval: null});
    }
  });

  props.field.annotations[annotationRowIndex.value].approval = annotation.approval;

  console.log(props.field.annotations);

  // 使用 nextTick 确保对话框已关闭后再触发事件
  nextTick(() => {
    // 直接触发编辑字段事件，由父组件处理更新逻辑
    emit("edit-field", props.field);
  });
}

function hasFillAnnotations(rowIndex: number) {
  var annotation = props.annotation ?? [];
  if (annotation.length - 1 >= rowIndex && annotation[rowIndex]?.fill) {
    return true;
  } else {
    var childHasFillAnnotations = props.field.fields.filter((f) => {
      var childAnnotation = f.annotations ?? [];
      if (
          childAnnotation.length - 1 >= rowIndex &&
          childAnnotation[rowIndex]?.fill
      ) {
        return true;
      } else {
        return false;
      }
    });
    return childHasFillAnnotations.length > 0;
  }

  return false;
}

// 获取基础子表单组件的引用
const baseSubForm = ref<InstanceType<typeof BaseSubFormField> | null>(null);

// 行数据
const rows = computed(() => {
  return props.value || [];
});
const previousRows = computed(() => {
  return props.previousValue || [];
});

const processedFields = computed(() =>
    processFieldsLayout(props.field.fields || [])
);

// 添加行
function addRow() {
  if (!props.field.fields || props.field.fields.length === 0) {
    return;
  }

  const newRow: Record<string, any> = {};
  props.field.fields.forEach((field: any) => {
    if (field.code) {
      newRow[field.code] = null;
    }
  });
  const newRows = [...rows.value, newRow];
  props.field.value = newRows;
  console.log("[addRow] props.field:", props.field);
  console.log("[addRow] props.field.value:", props.field.value);
  emit("update:value", newRows);
}

// 更新单元格值
function updateCellValue(rowIndex: number, fieldCode: string, value: any) {
  if (rowIndex >= 0 && rowIndex < rows.value.length && fieldCode) {
    const newRows = [...rows.value];
    newRows[rowIndex] = {...newRows[rowIndex], [fieldCode]: value};
    props.field.value = newRows;
    console.log("[updateCellValue] props.field:", props.field);
    console.log("[updateCellValue] props.field.value:", props.field.value);
    emit("update:value", newRows);
  }
}

// 删除行
function deleteRow(rowIndex: number) {
  if (rowIndex >= 0 && rowIndex < rows.value.length) {
    const newRows = [...rows.value];
    newRows.splice(rowIndex, 1);
    props.field.value = newRows;
    console.log("[deleteRow] props.field:", props.field);
    console.log("[deleteRow] props.field.value:", props.field.value);
    emit("update:value", newRows);
  }
}

// 上移行
function moveRowUp(rowIndex: number) {
  if (hasFillAnnotations(rowIndex - 1)) {
    return;
  }

  if (rowIndex > 0 && rowIndex < rows.value.length) {
    const newRows = [...rows.value];
    const temp = newRows[rowIndex];
    newRows[rowIndex] = newRows[rowIndex - 1];
    newRows[rowIndex - 1] = temp;
    props.field.value = newRows;
    console.log("[moveRowUp] props.field:", props.field);
    console.log("[moveRowUp] props.field.value:", props.field.value);
    emit("update:value", newRows);
  }
}

// 下移行
function moveRowDown(rowIndex: number) {
  if (rowIndex >= 0 && rowIndex < rows.value.length - 1) {
    const newRows = [...rows.value];
    const temp = newRows[rowIndex];
    newRows[rowIndex] = newRows[rowIndex + 1];
    newRows[rowIndex + 1] = temp;
    props.field.value = newRows;
    console.log("[moveRowDown] props.field:", props.field);
    console.log("[moveRowDown] props.field.value:", props.field.value);
    emit("update:value", newRows);
  }
}

// 设计模式下同步 value 字段
function syncDesignValue() {
  if (props.field.fields && props.field.fields.length > 0) {
    const row: Record<string, any> = {};
    props.field.fields.forEach((field: any) => {
      if (field.code) {
        row[field.code] = field.hasOwnProperty("value") ? field.value : "";
      }
    });
    props.field.value = [row];
  } else {
    props.field.value = [];
  }
  console.log("[syncDesignValue] props.field:", props.field);
  console.log("[syncDesignValue] props.field.value:", props.field.value);
}

// 更新子字段的值（设计模式下使用）
function updateSubFieldValue(index: number, value: any) {
  console.log("[updateSubFieldValue] index:", index, "value:", value);
  if (props.field.fields && props.field.fields[index]) {
    props.field.fields[index].value = value;
    syncDesignValue();
    emit("update:field", props.field);
  }
}

// 删除子字段（设计模式下使用）
function onDeleteSubField(fieldId: string) {
  if (props.field.fields) {
    const index = props.field.fields.findIndex((f: any) => f.id === fieldId);
    if (index !== -1) {
      props.field.fields.splice(index, 1);
      syncDesignValue();
      emit("update:field", props.field);
    }
  }
}

// 上移子字段（设计模式下使用）
function onMoveSubFieldUp({index}: { index: number }) {
  if (props.field.fields && index > 0) {
    const temp = props.field.fields[index];
    props.field.fields[index] = props.field.fields[index - 1];
    props.field.fields[index - 1] = temp;
    syncDesignValue();
    emit("update:field", props.field);
  }
}

// 下移子字段（设计模式下使用）
function onMoveSubFieldDown({index}: { index: number }) {
  if (props.field.fields && index < props.field.fields.length - 1) {
    const temp = props.field.fields[index];
    props.field.fields[index] = props.field.fields[index + 1];
    props.field.fields[index + 1] = temp;
    syncDesignValue();
    emit("update:field", props.field);
  }
}

</script>

<style scoped>
.field-actions {
  position: absolute;
  right: 4px;
  top: 4px;
  z-index: 10;
  display: none;
}

.multi-subform-instance {
  position: relative;
}

.multi-subform-instance.has-warning {
  border-color: var(--el-color-danger) !important;
  border: 1px dashed transparent;
  background-color: rgba(64, 158, 255, 0.05);
}

.multi-subform-instance.approval-mode:hover > .field-actions {
  display: block;
}

.multi-subform-instance.approval-mode {
  border: 1px dashed transparent;
  padding: 8px;
  margin-bottom: 8px;
}

.multi-subform-instance.approval-mode:hover {
  border-color: #409eff;
  background-color: rgba(64, 158, 255, 0.05);
}

.multi-subform-container {
  width: 100%;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 12px;
  background-color: #f9fafc;
}

.multi-subform-container.design-mode {
  border: 1px dashed #b3d8ff;
  background-color: #f0f7ff;
  transition: all 0.3s;
}

.multi-subform-container.design-mode.drag-over {
  border: 2px dashed #409eff;
  background-color: #ecf5ff;
  box-shadow: 0 0 8px rgba(64, 158, 255, 0.3);
}

.multi-subform-fields {
  width: 100%;
}

/* 设计模式下的样式 */
.subform-definition {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 12px;
  background-color: #fff;
}

.subform-definition-header {
  background-color: #f5f7fa;
  padding: 8px 12px;
  border-bottom: 1px solid #ebeef5;
}

.subform-definition-header h4 {
  margin: 0;
  font-size: 14px;
  color: #606266;
}

.subform-definition-content {
  padding: 12px;
}

.subform-field-row {
  margin-bottom: 12px;
}

.subform-field-row:last-child {
  margin-bottom: 0;
}

/* 非设计模式下的样式 */
.multi-subform-instance {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 16px;
  background-color: #fff;
}

.multi-subform-instance:last-child {
  margin-bottom: 0;
}

.multi-subform-instance-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f5f7fa;
  padding: 8px 12px;
  border-bottom: 1px solid #ebeef5;
}

.multi-subform-instance-header h4 {
  margin: 0;
  font-size: 14px;
  color: #606266;
}

.multi-subform-instance-content {
  padding: 16px;
}

.subform-field-label {
  font-weight: bold;
  margin-bottom: 4px;
  color: #606266;
}

.subform-field-content {
  margin-bottom: 12px;
}

.empty-subform,
.empty-multi-subform {
  padding: 20px;
  text-align: center;
  color: #909399;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px dashed #dcdfe6;
}

.multi-subform-actions {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 12px;
}

/* 修复子表单内控件的悬停问题 */
.multi-subform-container :deep(.form-field-row.design-mode) .field-actions {
  display: none;
}

.multi-subform-container
:deep(.form-field-row.design-mode:hover)
> .field-actions {
  display: block;
}

/* 确保子表单内的控件悬停不会影响其他控件 */
.multi-subform-container
:deep(
    .form-field-row.design-mode:hover
      ~ .form-field-row.design-mode
      .field-actions
  ) {
  display: none;
}
</style>
