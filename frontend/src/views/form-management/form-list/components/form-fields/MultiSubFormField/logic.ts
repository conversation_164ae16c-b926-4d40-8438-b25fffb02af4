// MultiSubFormField/logic.ts
export function validate(value: any, field: any, language: string) {
    const errors: string[] = [];
    if (!Array.isArray(value)) {
        errors.push((language == 'both' || language == 'zh') ? "子表单数据格式错误" : 'Subform data format error');
    }
    if (field.required && (!value || value.length === 0)) {
        errors.push((language == 'both' || language == 'zh') ? "请填写子表单" : 'Please fill in the subform');
    }
    if (field.extends?.minRows != null && value.length < field.extends.minRows) {
        errors.push((language == 'both' || language == 'zh') ? `最少${field.extends.minRows}行` : `At least ${field.extends.minRows} rows required`);
    }
    if (field.extends?.maxRows != null && value.length > field.extends.maxRows) {
        errors.push((language == 'both' || language == 'zh') ? `最多${field.extends.maxRows}行` : `At most ${field.extends.maxRows} rows allowed`);
    }
    return errors.join('；');
}