<template>
  <BaseField
    :field="field"
    :value="value"
    :previous-value="previousValue"
    :render-mode="renderMode"
    :formData="formData"
    :index="index"
    :total="total"
    :language="language"
    @delete-field="onDeleteField"
    @edit-field="(...args) => $emit('edit-field', ...args)"
    @move-up="onMoveUp"
    @move-down="onMoveDown"
  >
    <template #edit>
      <slot name="default"></slot>
    </template>
    <template #view>
      <slot name="default"></slot>
    </template>
  </BaseField>

  <!-- 字段选择器弹窗 -->
  <el-dialog
    v-model="showFieldSelector"
    title="选择要添加的控件类型"
    width="400px"
    :append-to-body="true"
    destroy-on-close
    class="field-selector-dialog"
  >
    <div class="field-selector-scroll">
      <div class="field-selector">
        <el-button
          v-for="type in availableFieldTypes"
          :key="type.type"
          @click="addField(type.type)"
          class="field-type-btn"
        >
          {{ type.labelZh }}/{{ type.labelEn }}
        </el-button>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="showFieldSelector = false">取消</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 字段选择器弹窗已经足够，不需要额外的字段编辑弹窗 -->
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits } from "vue";
import { ElButton, ElDialog, ElMessage } from "element-plus";
import { BaseField, type RenderMode } from "./index";
import { fieldTypeMap } from "./index";
import { nanoid } from "nanoid";

// 创建新控件
function createField(type: string) {
  // 公共字段
  let field: any = {
    id: nanoid(),
    code: `field_${nanoid(6)}`,
    type,
    newLine: false,
    labelZh: "",
    labelEn: "",
    required: false,
    unit: 6,
  };
  // 合并特有属性
  if (fieldTypeMap[type]?.getDefaultProps) {
    Object.assign(field, fieldTypeMap[type].getDefaultProps());
  }

  return field;
}

const props = defineProps({
  field: { type: Object, required: true },
  value: { type: [Object, Array], default: () => ({}) },
  previousValue: { type: [Object, Array], default: () => ({}) },
  renderMode: { type: String as () => RenderMode, default: "edit" },
  index: { type: Number },
  total: { type: Number },
  language: { type: String as () => "zh" | "en" | "both", default: "zh" },
  defaultLabel: { type: String, required: true },
  multiRow: { type: Boolean, default: false }, // 是否为多行子表单
  formData: {
    type: Object as PropType<Record<string, any>>,
    required: true,
  },
});

const emit = defineEmits([
  "update:value",
  "update:field",
  "delete-field",
  "edit-field",
  "move-up",
  "move-down",
]);

// 可用的字段类型 - 不包括子表单和多行子表单
const availableFieldTypes = [
  { type: "input", label: "单行文本" },
  { type: "textarea", label: "多行文本" },
  { type: "text_multilang", label: "多语言文本" },
  { type: "textarea_multilang", label: "多语言多行文本" },
  { type: "select", label: "下拉选择" },
  { type: "checkbox", label: "多选框" },
  { type: "radio", label: "单选框" },
  { type: "date", label: "日期" },
  { type: "number", label: "数字" },
  { type: "unit_select", label: "单位选择" },
  { type: "file", label: "文件" },
  { type: "int_range", label: "整数区间" },
  { type: "date_range", label: "日期区间" },
  // 子表单和多行子表单不能嵌套，所以这里不包括它们
];

// 字段选择器弹窗
const showFieldSelector = ref(false);

// 获取字段组件
function getFieldComponent(type: string) {
  return fieldTypeMap[type]?.component || fieldTypeMap["input"]?.component;
}

// 获取默认标签
function getDefaultLabel(type: string): string {
  const labelMap: Record<string, string> = {
    input: "单行文本",
    textarea: "多行文本",
    text_multilang: "多语言文本",
    textarea_multilang: "多语言多行文本",
    select: "下拉选择",
    checkbox: "多选框",
    radio: "单选框",
    date: "日期",
    number: "数字",
    unit_select: "单位选择",
    int_range: "整数区间",
    date_range: "日期区间",
  };
  return labelMap[type] || "表单控件";
}

// 添加字段
function addField(type: string) {
  // 检查是否是子表单或多行子表单，如果是则拒绝
  if (type === "subForm" || type === "multiSubForm") {
    ElMessage.warning("子表单或多行子表单中不能嵌套其他子表单");
    // 关闭选择器
    showFieldSelector.value = false;
    return;
  }

  const newField = createField(type);

  // 确保子表单有fields数组
  if (!props.field.fields) {
    props.field.fields = [];
  }

  // 添加新字段到子表单
  props.field.fields.push(newField);

  // 关闭选择器
  showFieldSelector.value = false;

  // 触发更新事件
  emit("update:field", props.field);
}

// 编辑子字段 - 直接传递给父组件处理
function onEditSubField(field: any) {
  // 直接触发编辑事件，由 BaseField 处理
  emit("edit-field", field);
}

// 处理子表单自身的事件
function onDeleteField(fieldId: string) {
  emit("delete-field", fieldId);
}



function onMoveUp(data: any) {
  emit("move-up", data);
}

function onMoveDown(data: any) {
  emit("move-down", data);
}

// 处理拖拽事件
function onDragOver(e: DragEvent) {
  if (props.renderMode !== "design") return;

  e.preventDefault();
  const type = e.dataTransfer?.getData("fieldType");

  // 只接受控件类型的拖拽，不接受分组，也不接受子表单和多行子表单
  if (
    type &&
    type !== "group" &&
    type !== "subForm" &&
    type !== "multiSubForm"
  ) {
    e.dataTransfer!.dropEffect = "copy";

    // 添加拖拽悬停样式
    const target = e.currentTarget as HTMLElement;
    if (target) {
      target.classList.add("drag-over");
    }
  } else if (type && (type === "subForm" || type === "multiSubForm")) {
    // 如果是子表单或多行子表单，显示禁止拖放的效果
    e.dataTransfer!.dropEffect = "none";
  }
}

function onDragLeave(e: DragEvent) {
  // 移除拖拽悬停样式
  const target = e.currentTarget as HTMLElement;
  if (target) {
    target.classList.remove("drag-over");
  }
}

function onDrop(e: DragEvent) {
  if (props.renderMode !== "design") return;

  e.preventDefault();
  e.stopPropagation();

  // 移除拖拽悬停样式
  const target = e.currentTarget as HTMLElement;
  if (target) {
    target.classList.remove("drag-over");
  }

  const type = e.dataTransfer?.getData("fieldType");

  // 只接受控件类型的拖拽，不接受分组
  if (type && type !== "group") {
    console.log("[DEBUG] 子表单接收到控件拖拽，类型:", type);

    // 检查是否是子表单或多行子表单，如果是则拒绝
    if (type === "subForm" || type === "multiSubForm") {
      // 使用 Element Plus 的消息提示
      ElMessage.warning("子表单或多行子表单中不能嵌套其他子表单");
      return;
    }

    // 创建新字段
    const newField = createField(type);

    // 确保子表单有fields数组
    if (!props.field.fields) {
      props.field.fields = [];
    }

    // 添加新字段到子表单
    props.field.fields.push(newField);

    // 触发更新事件
    emit("update:field", props.field);
  }
}

// 导出公共方法和属性，供子组件使用
defineExpose({
  getFieldComponent,
  getDefaultLabel,
  addField,
  onEditSubField,
  onDragOver,
  onDragLeave,
  onDrop,
  showFieldSelector,
  availableFieldTypes,
});
</script>

<style scoped>
.field-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.field-type-btn {
  margin: 5px;
  min-width: 100px;
}

.field-selector-scroll {
  max-height: 60vh;
  overflow-y: auto;
  padding: 5px 0;
}

.field-selector-dialog :deep(.el-dialog__body) {
  padding: 10px 20px;
}

/* 修复子表单内控件的悬停问题 - 基础样式 */
:deep(.form-field-row.design-mode) .field-actions {
  display: none !important;
}

:deep(.form-field-row.design-mode:hover) > .field-actions {
  display: block !important;
}

/* 确保子表单内的控件悬停不会影响其他控件 */
:deep(
    .form-field-row.design-mode:hover
      ~ .form-field-row.design-mode
      .field-actions
  ) {
  display: none !important;
}

/* 确保父容器的悬停不会影响子表单内的控件 */
:deep(
    .form-field-row.design-mode:hover .form-field-row.design-mode .field-actions
  ) {
  display: none !important;
}
</style>
