// 示例：输入框字段的校验逻辑
export function validate(value: any, field: any, language: string) {
    // 必填校验
    if (field.required && (!value || Object.keys(value).length === 0)) {
        return (language == 'both' || language == 'zh') ? "请上传文件" : 'Please upload a file';
    }
    // 兜底校验
    if (field.extends?.fileLimit && value?.fileSize > field.extends.fileLimit) {
        return (language == 'both' || language == 'zh') ? `文件${value.fileName}超出最大限制` : 'File ' + value.fileName + ' exceeds the maximum limit';
    }
    if (field.extends?.fileAccept && value?.fileName) {
        const ext = value.fileName.substring(value.fileName.lastIndexOf(".")).toLowerCase();
        const acceptArr = field.extends.fileAccept.split(",").map((s: string) => s.trim().toLowerCase());
        if (!acceptArr.some((a: string) => ext.endsWith(a))) {
            return (language == 'both' || language == 'zh') ? `文件${value.fileName}类型不被支持` : 'File ' + value.fileName + ' type is not supported';
        }
    }
    return '';
} 