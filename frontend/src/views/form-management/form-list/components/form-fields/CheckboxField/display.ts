import { FieldDto } from "@/dtos/dynamic-form.dto";

export function getDisplay(value: (string | number)[], field: FieldDto) {
    if (!value || value == null || value.length == 0) {
        return "--";
    }
    return value
        .map((item) => {
            const opt: any = field.options?.find(
                (opt: any) => opt.value === item
            );
            if (!opt) return item;
            // both
            return opt.labelZh && opt.labelEn
                ? opt.labelZh + " / " + opt.labelEn
                : opt.labelZh || opt.labelEn || opt.label || opt.value;
        })
        .join("、")
} 