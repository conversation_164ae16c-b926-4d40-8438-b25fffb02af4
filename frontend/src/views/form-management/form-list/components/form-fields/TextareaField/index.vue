<template>
  <div>
    <BaseField
      :field="field"
      :value="value"
      :previousValue="previousValue"
      :language="language"
      :rowIndex="rowIndex"
      :annotation="annotation"
      :formData="formData"
      :renderMode="renderMode"
      :index="index"
      :total="total"
      default-label="多行文本"
      @edit-field="(...args) => $emit('edit-field', ...args)"
      @delete-field="(...args) => $emit('delete-field', ...args)"
      @move-down="(...args) => $emit('move-down', ...args)"
      @move-up="(...args) => $emit('move-up', ...args)"
    >
      <template #edit="{ readonly }">
        <el-input
          v-model="textareaValue"
          type="textarea"
          :rows="4"
          :placeholder="field.placeholder || '请输入'"
          @change="updateValue"
          class="textarea-input"
          :disabled="readonly"
        />
        <div class="field-content" v-if="annotation?.fill">
          <el-text type="primary"> ⬆️AI提取内容：{{ annotation?.fill }}</el-text>
        </div>
      </template>
      <template #view>
        <div class="form-view-value">
          {{ getDisplay(textareaValue, field as FieldDto) }}
        </div>
      </template>
    </BaseField>
  </div>
</template>

<script setup lang="ts">
import { ElInput } from "element-plus";
import { BaseField, RenderMode } from "../index";
import { getDisplay } from "./display";
import { FieldDto } from "@/dtos/dynamic-form.dto";

import { ref, watch } from "vue";
import { validate as logicValidate } from "./logic"; // 你的校验逻辑


const props = defineProps({
  field: { type: Object, required: true },
  value: { type: String, default: "" },
  previousValue: { type: String, default: "" },
  annotation: { type: Object, required: false },
  renderMode: {
    type: String as () => RenderMode,
    default: "edit",
  },
  index: { type: Number },
  total: { type: Number },
  rowIndex: {
    type: Number,
    required: false,
  },
  language: { type: String as () => "zh" | "en" | "both", default: "zh" },
  formData: {
    type: Object as PropType<Record<string, any>>,
    required: true,
  },
});

const emit = defineEmits(["update:value"]);

const textareaValue = ref(props.value?.toString() || "");

// 监听外部值变化
watch(
  () => props.value,
  (newVal) => {
    textareaValue.value = newVal?.toString() || "";
  }
);

// 当值变化时更新父组件
function updateValue() {
  emit("update:value", textareaValue.value);
}

</script>

<style scoped>
.textarea-input {
  flex: 1;
}
</style>
