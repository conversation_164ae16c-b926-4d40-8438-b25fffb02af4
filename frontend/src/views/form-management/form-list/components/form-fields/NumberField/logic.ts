// 示例：数字字段的校验逻辑
export function validate(value: any, field: any, language: string) {
    if (field.required && (value === undefined || value === null || value === '')) {
        return (language == 'both' || language == 'zh') ? '该字段为必填项' : 'This field is required';
    }
    if (typeof value === 'number') {
        if (field.extends?.min !== undefined && value < field.extends.min) {
            return (language == 'both' || language == 'zh') ? `不能小于最小值${field.extends.min}` : `Cannot be less than minimum value ${field.extends.min}`;
        }
        if (field.extends?.max !== undefined && value > field.extends.max) {
            return (language == 'both' || language == 'zh') ? `不能大于最大值${field.extends.max}` : `Cannot be greater than maximum value ${field.extends.max}`;
        }
    }
    return '';
} 