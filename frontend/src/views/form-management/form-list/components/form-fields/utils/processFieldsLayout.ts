export function processFieldsLayout(fields: any[]) {

    const visibleFields = fields.filter(q => {
        return q.visible ?? true;
    });

    const result: any[] = [];
    let currentUnit = 0;
    let keyIdx = 0;
    let originIdx = 0;
    for (const f of visibleFields) {
        const field: any = f;
        let unit = Number(field.unit ?? 1);
        if (unit < 1) unit = 1;
        if (unit > 12) unit = 12;

        if (field.newLine && currentUnit > 0) {
            result.push({
                _empty: true,
                _span: (12 - currentUnit) * 2,
                _key: `empty-${keyIdx++}`
            });
            currentUnit = 0;
        }

        if (currentUnit + unit > 12) {
            if (currentUnit < 12 && currentUnit > 0) {
                result.push({
                    _empty: true,
                    _span: (12 - currentUnit) * 2,
                    _key: `empty-${keyIdx++}`
                });
            }
            currentUnit = 0;
        }

        result.push({...field, unit, _key: `field-${field.id}`, _originIndex: originIdx});
        currentUnit += unit;
        originIdx++;

        if (currentUnit === 12) {
            currentUnit = 0;
        }
    }
    if (currentUnit > 0 && currentUnit < 12) {
        result.push({
            _empty: true,
            _span: (12 - currentUnit) * 2,
            _key: `empty-last`
        });
    }
    return result;
} 