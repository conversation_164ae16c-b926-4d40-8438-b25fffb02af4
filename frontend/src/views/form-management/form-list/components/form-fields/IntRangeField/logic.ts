export function validate(value: any, field: any, language: string) {
    if ((value.min !== "" && isNaN(value.min)) || (value.max !== "" && isNaN(value.max))) {
        return (language == 'both' || language == 'zh') ? "请输入有效数字" : 'Please enter valid numbers';
    }
    if (value.min !== "" && value.max !== "" && Number(value.min) >= Number(value.max)) {
        return (language == 'both' || language == 'zh') ? "最小值必须小于最大值" : 'Minimum value must be less than maximum value';
    }
    if (field.required && (value.min === "" || value.max === "")) {
        return (language == 'both' || language == 'zh') ? "区间必填" : "Range is required";
    }
    if (field.extends?.min != null && value.min < field.extends.min) {
        return (language == 'both' || language == 'zh') ? `最小值不能小于${field.extends.min}` : `Minimum value cannot be less than ${field.extends.min}`;
    }
    if (field.extends?.max != null && value.max > field.extends.max) {
        return (language == 'both' || language == 'zh') ? `最大值不能大于${field.extends.max}` : `Maximum value cannot be greater than ${field.extends.max}`;
    }
    return '';
}