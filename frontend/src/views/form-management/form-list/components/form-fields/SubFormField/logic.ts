// SubFormField/logic.ts
export function validate(value: any, field: any, language: string) {
    const errors: string[] = [];
    if (typeof value !== 'object' || value == null || Array.isArray(value)) {
        errors.push((language == 'both' || language == 'zh') ? '子表单数据格式错误' : 'Subform data format error');
    }
    if (field.required && (!value || Object.keys(value).length === 0)) {
        errors.push((language == 'both' || language == 'zh') ? '请填写子表单' : 'Please fill in the form');
    }
    if (field.extends?.minFields != null && Object.keys(value).length < field.extends.minFields) {
        errors.push((language == 'both' || language == 'zh') ? `最少填写${field.extends.minFields}个字段` : `At least fill in ${field.extends.minFields} fields`);
    }
    if (field.extends?.maxFields != null && Object.keys(value).length > field.extends.maxFields) {
        errors.push((language == 'both' || language == 'zh') ? `最多填写${field.extends.maxFields}个字段` : `At most fill in ${field.extends.maxFields} fields`);
    }
    return errors.join('；');
}