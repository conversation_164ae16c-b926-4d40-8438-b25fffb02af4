<template>
  <div ref="selfRef">
    <BaseSubFormField
      ref="baseSubForm"
      :field="field"
      :value="value"
      :previousValue="previousValue"
      :language="language"
      :renderMode="renderMode"
      :formData="formData"
      :index="index"
      :total="total"
      default-label="子表单"
      :multi-row="false"
      @update:value="$emit('update:value', $event)"
      @update:field="$emit('update:field', $event)"
      @delete-field="$emit('delete-field', $event)"
      @move-up="$emit('move-up', $event)"
      @move-down="$emit('move-down', $event)"
      @edit-field="(...args) => $emit('edit-field', ...args)"
    >
      <div
        class="subform-container"
        :class="{ 'design-mode': renderMode === 'design' }"
        @dragover.prevent="baseSubForm?.onDragOver"
        @dragleave="baseSubForm?.onDragLeave"
        @drop="baseSubForm?.onDrop"
      >
        <!-- 子表单内容区域 -->
        <div class="subform-fields">
          <el-text type="primary" v-if="annotation && annotation.length > 0">{{
            annotation ? "⬇️AI提取内容：" + annotation[0].fill : ""
          }}</el-text>
          <el-row :gutter="16">
            <template v-for="item in processedFields" :key="item._key">
              <el-col v-if="item._empty" :span="item._span"></el-col>
              <el-col v-else :span="item.unit * 2">
                <div class="subform-field-row">
                  <component
                    :is="baseSubForm?.getFieldComponent(item.type)"
                    :field="item"
                    :value="item.value"
                    :annotation="item.annotations ? item.annotations[0] : null"
                    :in-subform="true"
                    :formData="formData"
                    :previous-value="previousValue?.[item.code] ?? null"
                    :render-mode="renderMode"
                    :index="item._originIndex"
                    :total="processedFields.length"
                    :language="language"
                    @update:value="(val: any) => updateSubFieldValue(item._originIndex, val)"
                    @delete-field="onDeleteSubField"
                    @edit-field="baseSubForm?.onEditSubField"
                    @move-up="onMoveSubFieldUp"
                    @move-down="onMoveSubFieldDown"
                  />
                </div>
              </el-col>
            </template>
          </el-row>
          <div
            v-if="!field.fields || field.fields.length === 0"
            class="empty-subform"
            @dragover.prevent="baseSubForm?.onDragOver"
            @dragleave="baseSubForm?.onDragLeave"
            @drop="baseSubForm?.onDrop"
          >
            <span v-if="renderMode === 'design'">请拖拽控件到此子表单</span>
            <span v-else>暂无数据</span>
          </div>
        </div>
      </div>
    </BaseSubFormField>
  </div>
</template>

<script setup lang="ts">
import BaseSubFormField from "../BaseSubFormField.vue";
import {BaseField, type RenderMode} from "../index";
import { processFieldsLayout } from "../utils/processFieldsLayout";
import { ref, watch, computed, onMounted, onUnmounted, getCurrentInstance } from "vue";
import { validate as logicValidate } from "./logic"; // 你的校验逻辑

const fieldRefs = ref<Record<string, any>>({});
const selfRef = ref();

interface SubFormValue {
  [key: string]: any;
}

const props = defineProps({
  
  field: { type: Object, required: true },
  value: { type: Object as () => SubFormValue, default: () => ({}) },
  previousValue: { type: Object as () => SubFormValue, default: () => ({}) },
  annotation: { type: Object, required: false },
  renderMode: { type: String as () => RenderMode, default: "edit" },
  index: { type: Number },
  total: { type: Number },
  language: { type: String as () => "zh" | "en" | "both", default: "zh" },
  formData: {
    type: Object as PropType<Record<string, any>>,
    required: true,
  },
});

const emit = defineEmits([
  "update:value",
  "update:field",
  "delete-field",
  "edit-field",
  "move-up",
  "move-down",
]);

// 获取基础子表单组件的引用
const baseSubForm = ref<InstanceType<typeof BaseSubFormField> | null>(null);

const processedFields = computed(() => {
  return processFieldsLayout(props.field.fields || []);
});

// 更新子字段的值
function updateSubFieldValue(index: number, value: any) {
  if (props.field.fields && props.field.fields[index]) {
    props.field.fields[index].value = value;

    // 更新整个子表单的值
    const newValue: SubFormValue = {};
    props.field.fields.forEach((field: any) => {
      if (field.code) {
        newValue[field.code] = field.value;
      }
    });

    emit("update:value", newValue);
  }
}

// 删除子字段
function onDeleteSubField(fieldId: string) {
  if (props.field.fields) {
    const index = props.field.fields.findIndex((f: any) => f.id === fieldId);
    if (index !== -1) {
      props.field.fields.splice(index, 1);

      // 更新整个子表单的值
      const newValue: SubFormValue = {};
      props.field.fields.forEach((field: any) => {
        if (field.code) {
          newValue[field.code] = field.value;
        }
      });

      emit("update:value", newValue);
    }
  }
}

// 上移子字段
function onMoveSubFieldUp({ index }: { index: number }) {
  if (props.field.fields && index > 0) {
    const temp = props.field.fields[index];
    props.field.fields[index] = props.field.fields[index - 1];
    props.field.fields[index - 1] = temp;
  }
}

// 下移子字段
function onMoveSubFieldDown({ index }: { index: number }) {
  if (props.field.fields && index < props.field.fields.length - 1) {
    const temp = props.field.fields[index];
    props.field.fields[index] = props.field.fields[index + 1];
    props.field.fields[index + 1] = temp;
  }
}

</script>

<style scoped>
.subform-container {
  width: 100%;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 12px;
  background-color: #f9fafc;
}

.subform-container.design-mode {
  border: 1px dashed #b3d8ff;
  background-color: #f0f7ff;
  transition: all 0.3s;
}

.subform-container.design-mode.drag-over {
  border: 2px dashed #409eff;
  background-color: #ecf5ff;
  box-shadow: 0 0 8px rgba(64, 158, 255, 0.3);
}

.subform-fields {
  margin-bottom: 12px;
}

.empty-subform {
  padding: 20px;
  text-align: center;
  color: #909399;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px dashed #dcdfe6;
}

/* 修复子表单内控件的悬停问题 */
.subform-container :deep(.form-field-row.design-mode) .field-actions {
  display: none;
}

.subform-container :deep(.form-field-row.design-mode:hover) > .field-actions {
  display: block;
}

/* 确保子表单内的控件悬停不会影响其他控件 */
.subform-container
  :deep(
    .form-field-row.design-mode:hover
      ~ .form-field-row.design-mode
      .field-actions
  ) {
  display: none;
}
</style>
