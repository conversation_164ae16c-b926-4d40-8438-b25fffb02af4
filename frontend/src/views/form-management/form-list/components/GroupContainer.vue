<template>
  <div class="group-container">
    <!-- 顶部插入占位符 -->
    <div
        v-if="renderMode === 'design' && index === 0"
        class="group-insert-placeholder"
        @dragover.prevent="onDragOverInsert(0)"
        @dragleave="onDragLeaveInsert(0)"
        @drop="onDropInsert(0, $event)"
        :class="{ 'drag-over-insert': dragOverInsertIdx === 0 }"
    >
      松开鼠标在此插入分组
    </div>
    <!-- 分组内容 -->
    <GroupBlock
        :group="group"
        :groups="groups"
        :fields="group.fields"
        :formData="formData"
        :group-idx="index"
        :group-count="totalGroups"
        :render-mode="renderMode"
        :language="language"
        @deleteField="onDeleteField"
        @moveField="onMoveField"
        @update:field="onUpdateField"
        @update-value="onUpdateValue"
    />
    <!-- 分组下方插入占位符 -->
    <div
        v-if="renderMode === 'design'"
        class="group-insert-placeholder"
        @dragover.prevent="onDragOverInsert(index + 1)"
        @dragleave="onDragLeaveInsert(index + 1)"
        @drop="onDropInsert(index + 1, $event)"
        :class="{ 'drag-over-insert': dragOverInsertIdx === index + 1 }"
    >
      松开鼠标在此插入分组
    </div>
  </div>
</template>

<script lang="ts">
import {defineComponent, ref, PropType} from "vue";
import GroupBlock from "./GroupBlock.vue";
import {type RenderMode} from "./form-fields/index";

export default defineComponent({
  name: "GroupContainer",
  components: {
    GroupBlock,
  },
  props: {
    group: {
      type: Object,
      required: true,
    },
    index: {
      type: Number,
      required: true,
    },
    totalGroups: {
      type: Number,
      required: true,
    },
    renderMode: {
      type: String as PropType<RenderMode>,
      default: "design",
    },
    language: {
      type: String as PropType<"zh" | "en" | "both">,
      default: "zh",
    },
    groups: {
      type: Array,
      required: true,
    },
    formData: {
      type: Object as PropType<Record<string, any>>,
      required: true,
    },
  },
  emits: ["drop-field", "update:field", "update-value"],
  setup(props, {emit}) {
    // 拖拽插入相关状态
    const dragOverInsertIdx = ref(-1);

    function onDragOverInsert(idx: number) {
      dragOverInsertIdx.value = idx;
    }

    function onDragLeaveInsert(idx: number) {
      if (dragOverInsertIdx.value === idx) dragOverInsertIdx.value = -1;
    }

    function onDropInsert(idx: number, e: DragEvent) {
      dragOverInsertIdx.value = -1;
      const type = e.dataTransfer?.getData("fieldType");
      if (
          type === "group" ||
          (typeof window !== "undefined" && (window as any).__draggingGroup)
      ) {
        // 直接在 groups 的 idx 位置插入新分组
        const uniqueId = Date.now() + "-" + Math.floor(Math.random() * 10000);
        const defaultCode = `grp_${uniqueId.toString()}`;
        const newGroup = {
          id: "g" + uniqueId,
          labelZh: `分组${props.groups.length + 1}`,
          labelEn: `Group${props.groups.length + 1}`,
          type: "group",
          code: defaultCode,
          fields: [],
        };
        props.groups.splice(idx, 0, newGroup);
        emit("update:field", {id: newGroup.id, updated: true});
      }
    }

    // 转发事件 - 简单地传递事件，不做额外处理
    function onDropField(event: any) {
      emit("drop-field", event);
    }

    function onDeleteField(fieldId: string) {
      // 不再 emit，直接由 GroupBlock 处理
    }

    function onMoveField(event: any) {
      // 不再 emit，直接由 GroupBlock 处理
    }

    function onUpdateField(field: any) {
      emit("update:field", field);
    }

    function onUpdateValue(field: any) {
      emit("update-value", field);
    }

    return {
      dragOverInsertIdx,
      onDragOverInsert,
      onDragLeaveInsert,
      onDropInsert,
      onDropField,
      onDeleteField,
      onMoveField,
      onUpdateField,
      onUpdateValue,
    };
  },
});
</script>

<style scoped>
.group-container {
  width: 100%;
}

.group-insert-placeholder {
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #aaa;
  background: #f8f8fa;
  border: 2px dashed transparent;
  border-radius: 6px;
  margin: 8px 0;
  font-size: 15px;
  transition: border-color 0.2s, background 0.2s;
}

.group-insert-placeholder.drag-over-insert {
  border-color: #409eff;
  background: #e6f7ff;
  color: #409eff;
}
</style>
