<template>
  <div class="expr-editor">
    <el-select v-model="type" @change="onTypeChange" style="width:180px">
      <el-option label="条件表达式 (if-then-else)" value="if" />
      <el-option label="普通逻辑表达式 (and/or/比较)" value="logic" />
    </el-select>
    <template v-if="type==='if'">
      <div class="expr-if-block">
        <div class="expr-if-row">
          <span class="expr-if-label">如果</span>
          <div class="expr-if-inner">
            <LogicExprEditor v-model="ifCondition" :all-fields="allFields" />
          </div>
        </div>
        <div class="expr-if-row">
          <span class="expr-if-label">则</span>
          <div class="expr-if-inner">
            <LogicExprEditor v-model="thenCondition" :all-fields="allFields" />
          </div>
        </div>
        <div class="expr-if-row">
          <span class="expr-if-label">否则</span>
          <div class="expr-if-inner">
            <LogicExprEditor v-model="elseCondition" :all-fields="allFields" />
          </div>
        </div>
      </div>
    </template>
    <template v-else>
      <LogicExprEditor v-model="logicExpression" :all-fields="allFields" />
    </template>
  </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue';
import LogicExprEditor from './LogicExprEditor.vue';
const props = defineProps({
  modelValue: { type: Object, required: true },
  allFields: { type: Array, default: () => [] }
});
const emit = defineEmits(['update:modelValue']);

const type = ref('logic');

// 计算属性用于处理 if-then-else 结构
const ifCondition = computed({
  get() {
    return props.modelValue.if || getDefaultLogic();
  },
  set(val) {
    emit('update:modelValue', {
      ...props.modelValue,
      if: val
    });
  }
});

const thenCondition = computed({
  get() {
    return props.modelValue.then || getDefaultLogic();
  },
  set(val) {
    emit('update:modelValue', {
      ...props.modelValue,
      then: val
    });
  }
});

const elseCondition = computed({
  get() {
    return props.modelValue.else || getDefaultLogic();
  },
  set(val) {
    emit('update:modelValue', {
      ...props.modelValue,
      else: val
    });
  }
});

// 计算属性用于处理普通逻辑表达式
const logicExpression = computed({
  get() {
    // 如果是 and/or 结构，直接返回
    if (props.modelValue.and || props.modelValue.or) {
      return props.modelValue;
    }
    // 如果是比较操作，包装在 and 中
    const compareOps = ['==', '!=', '>', '>=', '<', '<='];
    for (const op of compareOps) {
      if (props.modelValue[op]) {
        return props.modelValue;
      }
    }
    // 默认返回 and 结构
    return { and: [getDefaultLogic()] };
  },
  set(val) {
    emit('update:modelValue', val);
  }
});

watch(() => props.modelValue, (v) => {
  if (v && v.if !== undefined) {
    type.value = 'if';
  } else {
    type.value = 'logic';
  }
}, {immediate: true, deep: true});

function onTypeChange() {
  if (type.value === 'if') {
    emit('update:modelValue', {
      if: getDefaultLogic(),
      then: getDefaultLogic(),
      else: getDefaultLogic()
    });
  } else {
    emit('update:modelValue', { and: [getDefaultLogic()] });
  }
}

function getDefaultLogic() {
  return { and: [{ '==': [getDefaultLeaf(), getDefaultLeaf()] }] };
}
function getDefaultLeaf() {
  return { const: '', type: 'string' };
}
</script>

<script>
import LogicExprEditor from './LogicExprEditor.vue';
export default { components: { LogicExprEditor } };
</script>

<style scoped>
.expr-editor { margin-bottom: 8px; padding: 8px 0 8px 8px; }
.expr-if-block { background: #f7f9fa; border-radius: 8px; border: 1px solid #e3e6ea; padding: 12px 12px 8px 12px; margin: 8px 0; }
.expr-if-row { display: flex; align-items: flex-start; margin-bottom: 8px; }
.expr-if-label { min-width: 36px; font-weight: 500; color: #409eff; margin-right: 8px; margin-top: 4px; }
.expr-if-inner { flex: 1; }
.expr-logic-block {
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e3e6ea;
  padding: 10px 10px 6px 16px;
  margin: 8px 0;
}
.expr-logic-item {
  margin-bottom: 4px;
}
.expr-logic-divider {
  border-bottom: 1px dashed #d3d6db;
  margin: 4px 0 8px 0;
}
.expr-compare-block {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #f6f8fa;
  border-radius: 6px;
  border: 1px solid #e3e6ea;
  padding: 8px 12px;
  margin: 8px 0;
}
.expr-dialog-content {
  max-height: 60vh;
  overflow-y: auto;
  min-width: 600px;
  box-sizing: border-box;
}
</style>
