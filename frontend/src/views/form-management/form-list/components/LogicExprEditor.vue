<template>
  <div class="logic-expr-editor" :style="{ marginLeft: `${level * 16}px` }">
    <!-- 根级别：选择表达式类型 -->
    <div v-if="level === 0" class="root-type-selector">
      <el-select
        :model-value="rootType"
        style="width:200px;margin-bottom:12px;"
        @update:model-value="onRootTypeChange"
      >
        <el-option label="条件表达式 (if-then-else)" value="conditional" />
        <el-option label="直接比较表达式" value="logical" />
      </el-select>
    </div>

    <!-- 条件表达式 (if-then-else) -->
    <div v-if="isConditionalExpression" class="conditional-expression">
      <div class="condition-block">
        <div class="condition-row">
          <span class="condition-label">如果</span>
          <div class="condition-content">
            <LogicExprEditor
              :model-value="ifCondition"
              :all-fields="allFields"
              :level="level + 1"
              :force-type="'logical'"
              @update:model-value="onIfConditionChange"
            />
          </div>
        </div>
        <div class="condition-row">
          <span class="condition-label">则</span>
          <div class="condition-content">
            <LogicExprEditor
              :model-value="thenValue"
              :all-fields="allFields"
              :level="level + 1"
              :force-type="'logical'"
              @update:model-value="onThenValueChange"
            />
          </div>
        </div>
        <div class="condition-row">
          <span class="condition-label">否则</span>
          <div class="condition-content">
            <LogicExprEditor
              :model-value="elseValue"
              :all-fields="allFields"
              :level="level + 1"
              :force-type="'logical'"
              @update:model-value="onElseValueChange"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 逻辑比较表达式 -->
    <div v-else-if="isLogicalExpression" class="logical-expression">
      <!-- 逻辑操作符选择 (and/or) -->
      <div v-if="canSelectLogicOperator" class="logic-operator-selector">
        <el-select
          :model-value="logicOperator"
          style="width:120px;margin-bottom:8px;"
          @update:model-value="onLogicOperatorChange"
        >
          <el-option label="且 (and)" value="and" />
          <el-option label="或 (or)" value="or" />
        </el-select>
      </div>

      <!-- 逻辑条件列表 -->
      <div class="logic-conditions">
        <div v-for="(condition, idx) in logicConditions" :key="idx" class="logic-condition-item">
          <!-- 比较表达式 -->
          <div v-if="isComparisonCondition(condition)" class="comparison-condition">
            <div class="comparison-wrapper">
              <div class="comparison-operand">
                <ValueEditor
                  :model-value="getComparisonLeft(condition)"
                  :all-fields="allFields"
                  @update:model-value="(val) => onConditionLeftChange(idx, val)"
                />
              </div>
              <div class="comparison-operator">
                <el-select
                  :model-value="getComparisonOperator(condition)"
                  style="width:80px;"
                  @update:model-value="(val) => onComparisonOperatorChange(idx, val)"
                >
                  <el-option v-for="op in compareOps" :key="op" :label="op" :value="op" />
                </el-select>
              </div>
              <div class="comparison-operand">
                <ValueEditor
                  :model-value="getComparisonRight(condition)"
                  :all-fields="allFields"
                  @update:model-value="(val) => onConditionRightChange(idx, val)"
                />
              </div>
            </div>
          </div>

          <!-- 嵌套逻辑表达式 -->
          <div v-else class="nested-logic">
            <LogicExprEditor
              :model-value="logicConditions[idx]"
              :all-fields="allFields"
              :level="level + 1"
              :force-type="'logical'"
              @update:model-value="(val) => onNestedLogicChange(idx, val)"
            />
          </div>

          <!-- 删除按钮 -->
          <el-button
            v-if="logicConditions.length > 1"
            size="mini"
            type="danger"
            @click="removeCondition(idx)"
            style="margin-left:8px;"
          >
            删除
          </el-button>

          <!-- 逻辑操作符分隔线 -->
          <div v-if="idx < logicConditions.length - 1" class="logic-divider">
            <span class="logic-operator-text">{{ logicOperator.toUpperCase() }}</span>
          </div>
        </div>

        <!-- 添加条件按钮 -->
        <div class="add-condition-buttons">
          <el-button size="small" @click="addComparison">添加比较</el-button>
          <el-button size="small" @click="addNestedLogic">添加嵌套逻辑</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import LogicExprEditor from './LogicExprEditor.vue';
import ValueEditor from './ValueEditor.vue';

const props = defineProps({
  modelValue: { type: Object, required: true },
  allFields: { type: Array, default: () => [] },
  level: { type: Number, default: 0 },
  forceType: { type: String, default: '' } // 强制类型，用于嵌套时限制选择
});
const emit = defineEmits(['update:modelValue']);

const compareOps = ['==', '!=', '>', '>=', '<', '<='];

// 根级别类型选择
const rootType = ref('logical');

// 判断当前表达式类型
const isConditionalExpression = computed(() => {
  if (props.forceType === 'logical') return false;
  if (props.level === 0) return rootType.value === 'conditional';
  return safeModelValue.value && safeModelValue.value.if !== undefined;
});

const isLogicalExpression = computed(() => {
  if (props.forceType === 'logical') return true;
  if (props.level === 0) return rootType.value === 'logical';
  return !isConditionalExpression.value;
});

// 逻辑操作符
const logicOperator = ref('and');

const canSelectLogicOperator = computed(() => {
  return isLogicalExpression.value && props.level >= 0;
});

// 安全的 modelValue 访问
const safeModelValue = computed(() => {
  const result = props.modelValue || {};
  console.log('🔍 LogicExprEditor safeModelValue - props.modelValue:', props.modelValue);
  console.log('🔍 LogicExprEditor safeModelValue - result:', result);
  return result;
});

// 初始化和监听
watch(() => props.modelValue, (v) => {
  if (!v) return;

  // 检测根类型
  if (props.level === 0) {
    if (v.if !== undefined) {
      rootType.value = 'conditional';
    } else {
      rootType.value = 'logical';
    }
  }

  // 检测逻辑操作符
  if (v.and !== undefined) {
    logicOperator.value = 'and';
  } else if (v.or !== undefined) {
    logicOperator.value = 'or';
  }
}, { immediate: true, deep: true });

// 根类型变化处理
function onRootTypeChange(val) {
  rootType.value = val;
  let newVal;
  if (val === 'conditional') {
    newVal = {
      if: getDefaultComparison(),
      then: getDefaultValue(),
      else: getDefaultValue()
    };
  } else {
    newVal = {
      and: [getDefaultComparison()]
    };
  }
  emit('update:modelValue', newVal);
}

// 逻辑操作符变化处理
function onLogicOperatorChange(val) {
  logicOperator.value = val;
  const currentConditions = logicConditions.value;
  emit('update:modelValue', {
    [val]: currentConditions
  });
}

// 条件表达式的计算属性
const ifCondition = computed(() => {
  return safeModelValue.value.if || getDefaultComparison();
});

const thenValue = computed(() => {
  const originalThen = safeModelValue.value.then;
  console.log('🔍 LogicExprEditor thenValue - originalThen:', originalThen);
  console.log('🔍 LogicExprEditor thenValue - safeModelValue:', safeModelValue.value);

  const result = originalThen || { and: [getDefaultComparison()] };
  console.log('🔍 LogicExprEditor thenValue - result:', result);
  return result;
});

const elseValue = computed(() => {
  const originalElse = safeModelValue.value.else;
  console.log('🔍 LogicExprEditor elseValue - originalElse:', originalElse);
  console.log('🔍 LogicExprEditor elseValue - safeModelValue:', safeModelValue.value);

  const result = originalElse || { and: [getDefaultComparison()] };
  console.log('🔍 LogicExprEditor elseValue - result:', result);
  return result;
});

// 条件表达式事件处理
function onIfConditionChange(val) {
  emit('update:modelValue', {
    ...safeModelValue.value,
    if: val
  });
}

function onThenValueChange(val) {
  emit('update:modelValue', {
    ...safeModelValue.value,
    then: val
  });
}

function onElseValueChange(val) {
  emit('update:modelValue', {
    ...safeModelValue.value,
    else: val
  });
}

// 逻辑条件列表
const logicConditions = computed(() => {
  const v = safeModelValue.value;
  if (v.and) return v.and;
  if (v.or) return v.or;
  return [getDefaultComparison()];
});

// 更新逻辑条件列表
function updateLogicConditions(conditions) {
  emit('update:modelValue', {
    [logicOperator.value]: conditions
  });
}

// 判断是否为比较条件
function isComparisonCondition(condition) {
  if (!condition) return false;

  // 检查是否有比较操作符
  const hasComparisonOp = compareOps.some(op => condition[op] !== undefined);

  // 检查是否有嵌套逻辑操作符
  const hasLogicOp = condition.and !== undefined || condition.or !== undefined;

  // 如果有比较操作符，或者既没有逻辑操作符也没有比较操作符（默认为比较条件）
  return hasComparisonOp || !hasLogicOp;
}

// 获取比较条件的操作符
function getComparisonOperator(condition) {
  if (!condition) return '==';

  // 优先从 operator 字段获取
  if (condition.operator) return condition.operator;

  // 从比较操作符中查找
  for (const op of compareOps) {
    if (condition[op] !== undefined) return op;
  }

  return '==';
}

// 获取比较条件的左操作数
function getComparisonLeft(condition) {
  if (!condition) return getDefaultValue();

  // 优先从 left 字段获取
  if (condition.left) return condition.left;

  // 从比较操作符数组中获取
  const operator = getComparisonOperator(condition);
  if (condition[operator] && Array.isArray(condition[operator]) && condition[operator][0]) {
    return condition[operator][0];
  }

  return getDefaultValue();
}

// 获取比较条件的右操作数
function getComparisonRight(condition) {
  if (!condition) return getDefaultValue();

  // 优先从 right 字段获取
  if (condition.right) return condition.right;

  // 从比较操作符数组中获取
  const operator = getComparisonOperator(condition);
  if (condition[operator] && Array.isArray(condition[operator]) && condition[operator][1]) {
    return condition[operator][1];
  }

  return getDefaultValue();
}

// 比较条件事件处理
function onConditionLeftChange(idx, val) {
  const conditions = [...logicConditions.value];
  const originalCondition = conditions[idx];
  const condition = { ...originalCondition };

  // 获取当前的操作符和右侧值
  const operator = getComparisonOperator(originalCondition);
  const right = getComparisonRight(originalCondition);

  // 更新左侧值和比较条件格式
  condition.left = val;
  condition.operator = operator;
  condition[operator] = [val, right];

  conditions[idx] = condition;
  updateLogicConditions(conditions);
}

function onConditionRightChange(idx, val) {
  const conditions = [...logicConditions.value];
  const originalCondition = conditions[idx];
  const condition = { ...originalCondition };

  // 获取当前的操作符和左侧值
  const operator = getComparisonOperator(originalCondition);
  const left = getComparisonLeft(originalCondition);

  // 更新右侧值和比较条件格式
  condition.right = val;
  condition.operator = operator;
  condition[operator] = [left, val];

  conditions[idx] = condition;
  updateLogicConditions(conditions);
}

function onComparisonOperatorChange(idx, val) {
  const conditions = [...logicConditions.value];
  const originalCondition = conditions[idx];
  const condition = { ...originalCondition };

  // 获取当前的左右值
  const left = getComparisonLeft(originalCondition);
  const right = getComparisonRight(originalCondition);

  // 删除旧操作符
  const oldOperator = getComparisonOperator(originalCondition);
  if (oldOperator && condition[oldOperator]) {
    delete condition[oldOperator];
  }

  // 设置新操作符
  condition.operator = val;
  condition[val] = [left, right];

  conditions[idx] = condition;
  updateLogicConditions(conditions);
}

function onNestedLogicChange(idx, val) {
  const conditions = [...logicConditions.value];
  conditions[idx] = val;
  updateLogicConditions(conditions);
}

// 添加比较条件
function addComparison() {
  const conditions = [...logicConditions.value];
  conditions.push(getDefaultComparison());
  updateLogicConditions(conditions);
}

// 添加嵌套逻辑
function addNestedLogic() {
  const conditions = [...logicConditions.value];
  conditions.push({
    and: [getDefaultComparison()]
  });
  updateLogicConditions(conditions);
}

// 删除条件
function removeCondition(idx) {
  const conditions = [...logicConditions.value];
  conditions.splice(idx, 1);
  updateLogicConditions(conditions);
}

// 默认值生成函数
function getDefaultValue() {
  return { const: '', type: 'string' };
}

function getDefaultComparison() {
  const left = getDefaultValue();
  const right = getDefaultValue();
  return {
    '==': [left, right],
    left: left,
    right: right,
    operator: '=='
  };
}
</script>

<style scoped>
.logic-expr-editor {
  margin-bottom: 8px;
  border-radius: 6px;
  padding: 8px;
}

.root-type-selector {
  margin-bottom: 12px;
}

.conditional-expression {
  background: #f7f9fa;
  border: 1.5px solid #d1ecf1;
  border-radius: 8px;
  padding: 12px;
}

.condition-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
}

.condition-row:last-child {
  margin-bottom: 0;
}

.condition-label {
  min-width: 48px;
  font-weight: 600;
  color: #0c5460;
  margin-right: 12px;
  margin-top: 8px;
  font-size: 14px;
}

.condition-content {
  flex: 1;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #bee5eb;
  padding: 8px;
}

.logical-expression {
  background: #f8fafc;
  border: 1px solid #e3e6ea;
  border-radius: 6px;
  padding: 10px;
}

.logic-operator-selector {
  margin-bottom: 12px;
}

.logic-condition-item {
  margin-bottom: 8px;
  padding: 8px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #e3e6ea;
}

.comparison-condition {
  padding: 12px;
  background: #f9f9f9;
  border-radius: 6px;
  border: 1px solid #e3e6ea;
}

.comparison-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  flex-wrap: wrap;
}

.comparison-operand {
  flex: 1;
  min-width: 200px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  padding: 8px;
}

.comparison-operator {
  display: flex;
  align-items: center;
  margin-top: 8px;
}

.nested-logic {
  background: #f6f8fa;
  border-radius: 4px;
  padding: 4px;
}

.logic-divider {
  text-align: center;
  margin: 8px 0;
  position: relative;
}

.logic-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #ddd;
  z-index: 1;
}

.logic-operator-text {
  background: #f8fafc;
  padding: 2px 8px;
  font-size: 12px;
  color: #666;
  font-weight: 500;
  position: relative;
  z-index: 2;
}

.add-condition-buttons {
  margin-top: 12px;
  display: flex;
  gap: 8px;
}
</style>