<template>
  <div class="logic-expr-editor">
    <!-- 根级别：选择表达式类型 -->
    <div v-if="level === 0" class="root-type-selector">
      <el-select v-model="rootType" style="width:200px;margin-bottom:12px;" @change="onRootTypeChange">
        <el-option label="条件表达式 (if-then-else)" value="conditional" />
        <el-option label="逻辑比较表达式" value="logical" />
      </el-select>
    </div>

    <!-- and/or 递归 -->
    <div v-if="exprType==='and'||exprType==='or'" class="logic-block-inner">
      <div v-for="(item, idx) in logicArr" :key="idx" class="logic-item">
        <LogicExprEditor
          v-model="logicArr[idx]"
          :all-fields="allFields"
          :level="level + 1"
          :parent-type="exprType"
          :index="idx"
        />
        <el-button v-if="logicArr.length > 1" size="mini" type="danger" @click="removeLogic(idx)">删除</el-button>
        <div v-if="idx < logicArr.length - 1" class="logic-divider">
          <span class="logic-operator">{{ exprType.toUpperCase() }}</span>
        </div>
      </div>
      <el-button size="small" @click="addLogic">添加条件</el-button>
    </div>

    <!-- if-then-else 条件表达式 -->
    <div v-else-if="exprType==='if'" class="if-block-inner">
      <div class="if-condition-row">
        <span class="if-label">如果</span>
        <div class="if-condition-content">
          <LogicExprEditor
            v-model="ifCondition"
            :all-fields="allFields"
            :level="level + 1"
            :parent-type="exprType"
            :index="0"
          />
        </div>
      </div>
      <div class="if-condition-row">
        <span class="if-label">则</span>
        <div class="if-condition-content">
          <LogicExprEditor
            v-model="thenCondition"
            :all-fields="allFields"
            :level="level + 1"
            :parent-type="exprType"
            :index="1"
          />
        </div>
      </div>
      <div class="if-condition-row">
        <span class="if-label">否则</span>
        <div class="if-condition-content">
          <LogicExprEditor
            v-model="elseCondition"
            :all-fields="allFields"
            :level="level + 1"
            :parent-type="exprType"
            :index="2"
          />
        </div>
      </div>
    </div>

    <!-- 比较节点：三行布局 -->
    <div v-else-if="exprType==='compare'" class="compare-block-vertical">
      <div class="compare-side compare-left">
        <LogicExprEditor v-model="compareArr[0]" :all-fields="allFields" :level="level + 1" :parent-type="exprType" :index="0" />
      </div>
      <div class="compare-op-row">
        <el-select v-model="compareOp" style="width:80px;" @change="onCompareOpChange">
          <el-option v-for="op in compareOps" :key="op" :label="op" :value="op" />
        </el-select>
      </div>
      <div class="compare-side compare-right">
        <LogicExprEditor v-model="compareArr[1]" :all-fields="allFields" :level="level + 1" :parent-type="exprType" :index="1" />
      </div>
    </div>

    <!-- property 节点（多级属性链，纵向缩进） -->
    <div v-else-if="exprType==='property'" class="leaf-block-outer">
      <div class="property-card property-vertical">
        <div class="property-main-row">
          <el-select
            v-model="propertyArray[0]"
            placeholder="选择字段"
            style="width:140px"
            filterable
            @change="onPropertyChange"
          >
            <el-option v-for="f in allFields" :key="f.code" :label="f.labelZh || f.code" :value="f.code" />
          </el-select>
          <el-select v-model="propertyType" placeholder="类型" style="width:100px;margin-left:8px" @change="onPropertyTypeChange">
            <el-option label="字符串" value="string" />
            <el-option label="整数" value="int" />
            <el-option label="浮点" value="float" />
            <el-option label="日期" value="date" />
            <el-option label="布尔" value="boolean" />
          </el-select>
        </div>
        <div v-for="(p, idx) in propertyArray.slice(1)" :key="idx + 1" :class="'property-level property-level-' + (idx + 1)" :style="{ marginLeft: `${(idx + 1) * 20}px` }">
          <el-input
            v-model="propertyArray[idx + 1]"
            placeholder="属性名"
            style="width:100px"
            @input="onPropertyChange"
          />
          <el-button
            size="mini"
            type="danger"
            @click="removePropertyLevel(idx + 1)"
            style="margin-left:4px"
          >删除</el-button>
        </div>
        <el-button size="mini" @click="addPropertyLevel" style="margin-top:8px">添加属性层级</el-button>
      </div>
    </div>

    <!-- const 节点 -->
    <div v-else-if="exprType==='const'" class="leaf-block-inner">
      <el-input v-model="constValue" placeholder="常量值" style="width:120px" @input="onConstChange" />
      <el-select v-model="constType" placeholder="类型" style="width:100px;margin-left:8px" @change="onConstTypeChange">
        <el-option label="字符串" value="string" />
        <el-option label="整数" value="int" />
        <el-option label="浮点" value="float" />
        <el-option label="日期" value="date" />
        <el-option label="布尔" value="boolean" />
      </el-select>
    </div>

    <!-- func 节点 -->
    <div v-else-if="exprType==='func'" class="leaf-block-outer">
      <div class="func-card">
        <el-select v-model="funcName" placeholder="函数名" style="width:120px" @change="onFuncChange">
          <el-option v-for="fn in funcNameOptions" :key="fn" :label="fn" :value="fn" />
        </el-select>
        <el-select v-model="funcType" placeholder="返回类型" style="width:100px;margin-left:8px" @change="onFuncTypeChange">
          <el-option label="字符串" value="string" />
          <el-option label="整数" value="int" />
          <el-option label="浮点" value="float" />
          <el-option label="日期" value="date" />
          <el-option label="布尔" value="boolean" />
        </el-select>
        <div v-for="(arg, idx) in funcArgs" :key="idx" class="func-arg-card">
          <LogicExprEditor v-model="funcArgs[idx]" :all-fields="allFields" :level="level + 1" :parent-type="exprType" :index="idx" />
          <el-button size="mini" @click="removeFuncArg(idx)">删除参数</el-button>
        </div>
        <el-button size="small" @click="addFuncArg">添加参数</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import LogicExprEditor from './LogicExprEditor.vue';
import { dslFuncs } from '@/utils/dsl-funcs';

const props = defineProps({
  modelValue: { type: Object, required: true },
  allFields: { type: Array, default: () => [] },
  level: { type: Number, default: 0 },
  parentType: { type: String, default: '' },
  index: { type: Number, default: 0 }
});
const emit = defineEmits(['update:modelValue']);

const compareOps = ['==', '!=', '>', '>=', '<', '<='];
const funcNameOptions = computed(() => Object.keys(dslFuncs));

const isHover = ref(false);

const blockBgColor = computed(() => {
  if (props.level === 0) return '#f8fafc';
  if (props.parentType === 'and') return '#eaf6ff';
  if (props.parentType === 'or') return '#f9f3e6';
  if (props.parentType === 'compare') return '#f6f8fa';
  if (props.parentType === 'if') return '#f7f9fa';
  return '#fff';
});

// 类型识别
const exprType = ref('and');
watch(() => props.modelValue, (v) => {
  if (!v) return;
  if (v.and !== undefined) exprType.value = 'and';
  else if (v.or !== undefined) exprType.value = 'or';
  else if (v.if !== undefined) exprType.value = 'if';
  else if (compareOps.some(op => v[op] !== undefined)) exprType.value = 'compare';
  else if (v.property !== undefined) exprType.value = 'property';
  else if (v.const !== undefined) exprType.value = 'const';
  else if (v.func !== undefined) exprType.value = 'func';
}, { immediate: true, deep: true });

function onTypeChange() {
  let newVal;
  if (exprType.value === 'and' || exprType.value === 'or') {
    newVal = { [exprType.value]: [getDefaultLogic()] };
  } else if (exprType.value === 'if') {
    newVal = {
      if: getDefaultLogic(),
      then: getDefaultLeaf(),
      else: getDefaultLeaf()
    };
  } else if (exprType.value === 'compare') {
    newVal = { '==': [getDefaultLeaf(), getDefaultLeaf()] };
  } else if (exprType.value === 'property') {
    newVal = { property: [''], type: 'string' };
  } else if (exprType.value === 'const') {
    newVal = { const: '', type: 'string' };
  } else if (exprType.value === 'func') {
    newVal = { func: funcNameOptions.value[0] || '', args: [], type: 'string' };
  }
  emit('update:modelValue', newVal, props.level, props.parentType, props.index);
}

// and/or
const logicArr = computed({
  get() {
    return props.modelValue[exprType.value] || [];
  },
  set(val) {
    emit('update:modelValue', { [exprType.value]: val }, props.level, props.parentType, props.index);
  }
});
function addLogic() {
  logicArr.value.push(getDefaultLogic());
}
function removeLogic(idx) {
  logicArr.value.splice(idx, 1);
}

// if-then-else 处理
const ifCondition = computed({
  get() {
    return props.modelValue.if || getDefaultLogic();
  },
  set(val) {
    emit('update:modelValue', {
      ...props.modelValue,
      if: val
    }, props.level, props.parentType, props.index);
  }
});

const thenCondition = computed({
  get() {
    return props.modelValue.then || getDefaultLeaf();
  },
  set(val) {
    emit('update:modelValue', {
      ...props.modelValue,
      then: val
    }, props.level, props.parentType, props.index);
  }
});

const elseCondition = computed({
  get() {
    return props.modelValue.else || getDefaultLeaf();
  },
  set(val) {
    emit('update:modelValue', {
      ...props.modelValue,
      else: val
    }, props.level, props.parentType, props.index);
  }
});

// 比较
const compareOp = ref('==');
const compareArr = computed({
  get() {
    const op = compareOps.find(op => props.modelValue[op] !== undefined) || '==';
    compareOp.value = op; // 同步操作符
    return props.modelValue[op] || [getDefaultLeaf(), getDefaultLeaf()];
  },
  set(val) {
    emit('update:modelValue', { [compareOp.value]: val }, props.level, props.parentType, props.index);
  }
});

watch(() => props.modelValue, (v) => {
  if (!v) return;
  const op = compareOps.find(op => v[op] !== undefined);
  if (op && op !== compareOp.value) {
    compareOp.value = op;
  }
}, { immediate: true, deep: true });

function onCompareOpChange() {
  const currentValues = compareArr.value;
  emit('update:modelValue', { [compareOp.value]: currentValues }, props.level, props.parentType, props.index);
}

// func
function addFuncArg() {
  const currentValue = { ...props.modelValue };
  if (!currentValue.args) currentValue.args = [];
  currentValue.args.push(getDefaultLeaf());
  emit('update:modelValue', currentValue, props.level, props.parentType, props.index);
}
function removeFuncArg(idx) {
  const currentValue = { ...props.modelValue };
  if (!currentValue.args) return;
  currentValue.args.splice(idx, 1);
  emit('update:modelValue', currentValue, props.level, props.parentType, props.index);
}

// property 多级处理
const propertyArray = computed({
  get() {
    return props.modelValue.property || [''];
  },
  set(val) {
    emit('update:modelValue', {
      ...props.modelValue,
      property: val,
      type: props.modelValue.type || 'string'
    }, props.level, props.parentType, props.index);
  }
});

const propertyType = computed({
  get() {
    return props.modelValue.type || 'string';
  },
  set(val) {
    emit('update:modelValue', {
      ...props.modelValue,
      type: val
    }, props.level, props.parentType, props.index);
  }
});

function addPropertyLevel() {
  const newArray = [...propertyArray.value, ''];
  emit('update:modelValue', {
    ...props.modelValue,
    property: newArray,
    type: propertyType.value
  }, props.level, props.parentType, props.index);
}

function removePropertyLevel(idx) {
  if (propertyArray.value.length > 1) {
    const newArray = [...propertyArray.value];
    newArray.splice(idx, 1);
    emit('update:modelValue', {
      ...props.modelValue,
      property: newArray,
      type: propertyType.value
    }, props.level, props.parentType, props.index);
  }
}

function onPropertyChange() {
  // 触发更新
  emit('update:modelValue', {
    ...props.modelValue,
    property: propertyArray.value,
    type: propertyType.value
  }, props.level, props.parentType, props.index);
}

function onPropertyTypeChange() {
  // 触发更新
  emit('update:modelValue', {
    ...props.modelValue,
    property: propertyArray.value,
    type: propertyType.value
  }, props.level, props.parentType, props.index);
}

// const 节点处理
const constValue = computed({
  get() {
    return props.modelValue.const || '';
  },
  set(val) {
    emit('update:modelValue', {
      ...props.modelValue,
      const: val
    }, props.level, props.parentType, props.index);
  }
});

const constType = computed({
  get() {
    return props.modelValue.type || 'string';
  },
  set(val) {
    emit('update:modelValue', {
      ...props.modelValue,
      type: val
    }, props.level, props.parentType, props.index);
  }
});

function onConstChange() {
  // 触发更新已在computed中处理
}

function onConstTypeChange() {
  // 触发更新已在computed中处理
}

// func 节点处理
const funcName = computed({
  get() {
    return props.modelValue.func || '';
  },
  set(val) {
    emit('update:modelValue', {
      ...props.modelValue,
      func: val
    }, props.level, props.parentType, props.index);
  }
});

const funcType = computed({
  get() {
    return props.modelValue.type || 'string';
  },
  set(val) {
    emit('update:modelValue', {
      ...props.modelValue,
      type: val
    }, props.level, props.parentType, props.index);
  }
});

const funcArgs = computed({
  get() {
    return props.modelValue.args || [];
  },
  set(val) {
    emit('update:modelValue', {
      ...props.modelValue,
      args: val
    }, props.level, props.parentType, props.index);
  }
});

function onFuncChange() {
  // 触发更新已在computed中处理
}

function onFuncTypeChange() {
  // 触发更新已在computed中处理
}

function getDefaultLogic() {
  return { '==': [getDefaultLeaf(), getDefaultLeaf()] };
}
function getDefaultLeaf() {
  return { const: '', type: 'string' };
}
</script>

<style scoped>
.logic-expr-editor {
  margin-bottom: 8px;
  border-radius: 6px;
  transition: box-shadow 0.2s, border-color 0.2s;
  position: relative;
  padding: 10px 10px 6px 8px;
}
.logic-expr-editor.is-hover {
  box-shadow: 0 0 0 2px #409eff33;
  border-color: #409eff;
  z-index: 2;
}
.logic-block-inner {
  background: none;
}
.compare-block-vertical {
  background: none;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 0;
  margin: 8px 0;
}
.compare-side {
  width: 100%;
  margin-bottom: 2px;
}
.compare-op-row {
  width: 100%;
  display: flex;
  justify-content: center;
  margin: 2px 0 2px 0;
}
.leaf-block-inner {
  background: none;
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 8px 0;
}
.leaf-block-outer {
  background: none;
  margin: 8px 0;
}
.property-card {
  border: 1.5px solid #b3d8ff;
  border-radius: 6px;
  background: #f6fbff;
  padding: 10px 12px 8px 12px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 0;
}
.property-vertical {
  padding-bottom: 12px;
}
.property-level {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}
.func-card {
  border: 1.5px solid #ffe0b2;
  border-radius: 6px;
  background: #fffaf3;
  padding: 10px 12px 8px 12px;
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  gap: 8px;
}
.func-arg-card {
  border: 1px dashed #ffd180;
  border-radius: 4px;
  background: #fffbe7;
  padding: 8px 8px 4px 8px;
  margin: 4px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}
.logic-item { margin-bottom: 4px; }
.logic-divider {
  border-bottom: 1px dashed #d3d6db;
  margin: 4px 0 8px 0;
  position: relative;
  text-align: center;
}
.logic-operator {
  background: #f8fafc;
  padding: 2px 8px;
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

/* if-then-else 样式 */
.if-block-inner {
  background: #f7f9fa;
  border: 1.5px solid #d1ecf1;
  border-radius: 8px;
  padding: 12px;
  margin: 8px 0;
}
.if-condition-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
}
.if-condition-row:last-child {
  margin-bottom: 0;
}
.if-label {
  min-width: 48px;
  font-weight: 600;
  color: #0c5460;
  margin-right: 12px;
  margin-top: 8px;
  font-size: 14px;
}
.if-condition-content {
  flex: 1;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #bee5eb;
  padding: 4px;
}
</style>