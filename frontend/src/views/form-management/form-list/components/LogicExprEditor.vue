<template>
  <div
    class="logic-expr-editor"
    :class="[exprType + '-block', { 'is-hover': isHover }]"
    :style="{ marginLeft: `${level * 16}px`, borderLeft: level > 0 ? '3px solid #e3e6ea' : 'none', background: blockBgColor }"
    @mouseenter="isHover = true"
    @mouseleave="isHover = false"
  >
    <!-- 类型选择 -->
    <el-select v-model="exprType" style="width:120px;margin-bottom:4px;" @change="onTypeChange">
      <el-option label="且 (and)" value="and" />
      <el-option label="或 (or)" value="or" />
      <el-option label="比较" value="compare" />
      <el-option label="字段 (property)" value="property" />
      <el-option label="常量 (const)" value="const" />
      <el-option label="函数 (func)" value="func" />
    </el-select>

    <!-- and/or 递归 -->
    <div v-if="exprType==='and'||exprType==='or'" class="logic-block-inner">
      <div v-for="(item, idx) in logicArr" :key="idx" class="logic-item">
        <LogicExprEditor
          v-model="logicArr[idx]"
          :all-fields="allFields"
          :level="level + 1"
          :parent-type="exprType"
          :index="idx"
        />
        <el-button v-if="logicArr.length > 1" size="mini" type="danger" @click="removeLogic(idx)">删除</el-button>
        <div v-if="idx < logicArr.length - 1" class="logic-divider"></div>
      </div>
      <el-button size="small" @click="addLogic">添加条件</el-button>
    </div>

    <!-- 比较节点：三行布局 -->
    <div v-else-if="exprType==='compare'" class="compare-block-vertical">
      <div class="compare-side compare-left">
        <LogicExprEditor v-model="compareArr[0]" :all-fields="allFields" :level="level + 1" :parent-type="exprType" :index="0" />
      </div>
      <div class="compare-op-row">
        <el-select v-model="compareOp" style="width:80px;" @change="onCompareOpChange">
          <el-option v-for="op in compareOps" :key="op" :label="op" :value="op" />
        </el-select>
      </div>
      <div class="compare-side compare-right">
        <LogicExprEditor v-model="compareArr[1]" :all-fields="allFields" :level="level + 1" :parent-type="exprType" :index="1" />
      </div>
    </div>

    <!-- property 节点（多级属性链，纵向缩进） -->
    <div v-else-if="exprType==='property'" class="leaf-block-outer">
      <div class="property-card property-vertical">
        <div v-for="(p, idx) in modelValue.property" :key="idx" :class="'property-level property-level-' + idx" :style="{ marginLeft: `${idx * 20}px` }">
          <el-select
            v-if="idx === 0"
            v-model="modelValue.property[0]"
            placeholder="选择字段"
            style="width:140px"
            filterable
          >
            <el-option v-for="f in allFields" :key="f.code" :label="f.labelZh || f.code" :value="f.code" />
          </el-select>
          <el-input
            v-else
            v-model="modelValue.property[idx]"
            placeholder="属性名"
            style="width:100px"
          />
          <el-button
            v-if="modelValue.property.length > 1"
            size="mini"
            type="danger"
            @click="removePropertyLevel(idx)"
            style="margin-left:4px"
          >删除</el-button>
        </div>
        <el-button size="mini" @click="addPropertyLevel" style="margin-top:8px">添加属性层级</el-button>
        <el-select v-model="modelValue.type" placeholder="类型" style="width:100px;margin-left:8px">
          <el-option label="字符串" value="string" />
          <el-option label="整数" value="int" />
          <el-option label="浮点" value="float" />
          <el-option label="日期" value="date" />
          <el-option label="布尔" value="boolean" />
        </el-select>
      </div>
    </div>

    <!-- const 节点 -->
    <div v-else-if="exprType==='const'" class="leaf-block-inner">
      <el-input v-model="modelValue.const" placeholder="常量值" style="width:120px" />
      <el-select v-model="modelValue.type" placeholder="类型" style="width:100px;margin-left:8px">
        <el-option label="字符串" value="string" />
        <el-option label="整数" value="int" />
        <el-option label="浮点" value="float" />
        <el-option label="日期" value="date" />
        <el-option label="布尔" value="boolean" />
      </el-select>
    </div>

    <!-- func 节点 -->
    <div v-else-if="exprType==='func'" class="leaf-block-outer">
      <div class="func-card">
        <el-select v-model="modelValue.func" placeholder="函数名" style="width:120px">
          <el-option v-for="fn in funcNameOptions" :key="fn" :label="fn" :value="fn" />
        </el-select>
        <el-select v-model="modelValue.type" placeholder="返回类型" style="width:100px;margin-left:8px">
          <el-option label="字符串" value="string" />
          <el-option label="整数" value="int" />
          <el-option label="浮点" value="float" />
          <el-option label="日期" value="date" />
          <el-option label="布尔" value="boolean" />
        </el-select>
        <div v-for="(arg, idx) in modelValue.args" :key="idx" class="func-arg-card">
          <LogicExprEditor v-model="modelValue.args[idx]" :all-fields="allFields" :level="level + 1" :parent-type="exprType" :index="idx" />
          <el-button size="mini" @click="removeFuncArg(idx)">删除参数</el-button>
        </div>
        <el-button size="small" @click="addFuncArg">添加参数</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import LogicExprEditor from './LogicExprEditor.vue';
import { dslFuncs } from '@/utils/dsl-funcs';

const props = defineProps({
  modelValue: { type: Object, required: true },
  allFields: { type: Array, default: () => [] },
  level: { type: Number, default: 0 },
  parentType: { type: String, default: '' },
  index: { type: Number, default: 0 }
});
const emit = defineEmits(['update:modelValue']);

const compareOps = ['==', '!=', '>', '>=', '<', '<='];
const funcNameOptions = computed(() => Object.keys(dslFuncs));

const isHover = ref(false);

const blockBgColor = computed(() => {
  if (props.level === 0) return '#f8fafc';
  if (props.parentType === 'and') return '#eaf6ff';
  if (props.parentType === 'or') return '#f9f3e6';
  if (props.parentType === 'compare') return '#f6f8fa';
  if (props.parentType === 'if') return '#f7f9fa';
  return '#fff';
});

// 类型识别
const exprType = ref('and');
watch(() => props.modelValue, (v) => {
  if (!v) return;
  if (v.and !== undefined) exprType.value = 'and';
  else if (v.or !== undefined) exprType.value = 'or';
  else if (compareOps.some(op => v[op] !== undefined)) exprType.value = 'compare';
  else if (v.property !== undefined) exprType.value = 'property';
  else if (v.const !== undefined) exprType.value = 'const';
  else if (v.func !== undefined) exprType.value = 'func';
}, { immediate: true, deep: true });

function onTypeChange() {
  let newVal;
  if (exprType.value === 'and' || exprType.value === 'or') {
    newVal = { [exprType.value]: [getDefaultLogic()] };
  } else if (exprType.value === 'compare') {
    newVal = { '==': [getDefaultLeaf(), getDefaultLeaf()] };
  } else if (exprType.value === 'property') {
    newVal = { property: [''], type: 'string' };
  } else if (exprType.value === 'const') {
    newVal = { const: '', type: 'string' };
  } else if (exprType.value === 'func') {
    newVal = { func: funcNameOptions.value[0] || '', args: [], type: 'string' };
  }
  emit('update:modelValue', newVal, props.level, props.parentType, props.index);
}

// and/or
const logicArr = computed({
  get() {
    return props.modelValue[exprType.value] || [];
  },
  set(val) {
    emit('update:modelValue', { [exprType.value]: val }, props.level, props.parentType, props.index);
  }
});
function addLogic() {
  logicArr.value.push(getDefaultLogic());
}
function removeLogic(idx) {
  logicArr.value.splice(idx, 1);
}

// 比较
const compareOp = ref('==');
const compareArr = computed({
  get() {
    const op = compareOps.find(op => props.modelValue[op] !== undefined) || '==';
    return props.modelValue[op] || [getDefaultLeaf(), getDefaultLeaf()];
  },
  set(val) {
    emit('update:modelValue', { [compareOp.value]: val }, props.level, props.parentType, props.index);
  }
});
watch(() => props.modelValue, (v) => {
  if (!v) return;
  const op = compareOps.find(op => v[op] !== undefined);
  if (op) compareOp.value = op;
}, { immediate: true, deep: true });
function onCompareOpChange() {
  emit('update:modelValue', { [compareOp.value]: [getDefaultLeaf(), getDefaultLeaf()] }, props.level, props.parentType, props.index);
}

// func
function addFuncArg() {
  if (!modelValue.args) modelValue.args = [];
  modelValue.args.push(getDefaultLeaf());
  emit('update:modelValue', { ...modelValue }, props.level, props.parentType, props.index);
}
function removeFuncArg(idx) {
  if (!modelValue.args) return;
  modelValue.args.splice(idx, 1);
  emit('update:modelValue', { ...modelValue }, props.level, props.parentType, props.index);
}

// property 多级
function addPropertyLevel() {
  modelValue.property.push('');
  emit('update:modelValue', { ...modelValue }, props.level, props.parentType, props.index);
}
function removePropertyLevel(idx) {
  modelValue.property.splice(idx, 1);
  emit('update:modelValue', { ...modelValue }, props.level, props.parentType, props.index);
}

function getDefaultLogic() {
  return { '==': [getDefaultLeaf(), getDefaultLeaf()] };
}
function getDefaultLeaf() {
  return { const: '', type: 'string' };
}

const modelValue = props.modelValue;
</script>

<style scoped>
.logic-expr-editor {
  margin-bottom: 8px;
  border-radius: 6px;
  transition: box-shadow 0.2s, border-color 0.2s;
  position: relative;
  padding: 10px 10px 6px 8px;
}
.logic-expr-editor.is-hover {
  box-shadow: 0 0 0 2px #409eff33;
  border-color: #409eff;
  z-index: 2;
}
.logic-block-inner {
  background: none;
}
.compare-block-vertical {
  background: none;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 0;
  margin: 8px 0;
}
.compare-side {
  width: 100%;
  margin-bottom: 2px;
}
.compare-op-row {
  width: 100%;
  display: flex;
  justify-content: center;
  margin: 2px 0 2px 0;
}
.leaf-block-inner {
  background: none;
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 8px 0;
}
.leaf-block-outer {
  background: none;
  margin: 8px 0;
}
.property-card {
  border: 1.5px solid #b3d8ff;
  border-radius: 6px;
  background: #f6fbff;
  padding: 10px 12px 8px 12px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 0;
}
.property-vertical {
  padding-bottom: 12px;
}
.property-level {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}
.func-card {
  border: 1.5px solid #ffe0b2;
  border-radius: 6px;
  background: #fffaf3;
  padding: 10px 12px 8px 12px;
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  gap: 8px;
}
.func-arg-card {
  border: 1px dashed #ffd180;
  border-radius: 4px;
  background: #fffbe7;
  padding: 8px 8px 4px 8px;
  margin: 4px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}
.logic-item { margin-bottom: 4px; }
.logic-divider { border-bottom: 1px dashed #d3d6db; margin: 4px 0 8px 0; }
</style>