<template>
  <div class="date-property-editor">
    <el-form-item label="文件类型编码">
      <el-input v-model="typeCode" @input="onChange" />
    </el-form-item>
    <!-- <el-form-item label="允许后缀名">
      <el-input v-model="accept" @input="onChange" />
    </el-form-item> -->
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from "vue";
import { ElFormItem, ElInput } from "element-plus";

const props = defineProps({
  field: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(["change"]);

// 确保 extends 对象存在
if (!props.field.extends) {
  props.field.extends = {};
}

// 本地响应式数据
const typeCode = ref(props.field.extends.typeCode);
// const accept = ref(props.field.extends.accept);

// 监听属性变化
watch(typeCode, (newVal) => {
  props.field.extends.typeCode = newVal;
  onChange();
});
// watch(accept, (newVal) => {
//   props.field.extends.accept = newVal;
//   onChange();
// });

// 初始化
onMounted(() => {});

// 当字段属性变更时触发
function onChange() {
  emit("change", props.field);
}
</script>

<style scoped>
.file-property-editor {
  /* 日期区间字段属性编辑器样式 */
}
</style>
