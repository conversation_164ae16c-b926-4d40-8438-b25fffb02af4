<template>
  <div class="options-property-editor">
    <el-form-item label="选项">
      <div class="options-list">
        <div
          v-for="(opt, idx) in options"
          :key="idx"
          class="option-item"
        >
          <div class="option-inputs">
            <el-input
              v-model="opt.labelZh"
              placeholder="中文"
              class="option-input"
              @input="onChange"
            />
            <el-input
              v-model="opt.labelEn"
              placeholder="English"
              class="option-input"
              @input="onChange"
            />
            <el-input
              v-model="opt.value"
              placeholder="值"
              class="option-input"
              @input="onChange"
            />
          </div>
          <el-button
            type="danger"
            size="default"
            @click="removeOption(idx)"
            class="delete-option-btn"
          >删除</el-button>
        </div>
        <el-button
          type="primary"
          @click="addOption"
          class="add-option-btn"
        >添加选项</el-button>
      </div>
    </el-form-item>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import { ElFormItem, ElInput, ElButton } from 'element-plus';

const props = defineProps({
  field: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['change']);

// 确保 options 数组存在
if (!props.field.options) {
  props.field.options = [];
}

// 本地响应式数据
const options = ref(props.field.options);

// 添加选项
function addOption() {
  options.value.push({ labelZh: '', labelEn: '', value: '' });
  onChange();
}

// 删除选项
function removeOption(idx: number) {
  options.value.splice(idx, 1);
  onChange();
}

// 当字段属性变更时触发
function onChange() {
  emit('change', props.field);
}
</script>

<style scoped>
.options-property-editor {
  /* 选项类字段属性编辑器样式 */
}

/* 选项列表样式 */
.options-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
}

.option-item {
  display: flex;
  gap: 12px;
  align-items: flex-start;
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 4px;
  border: 1px solid #ebeef5;
}

.option-inputs {
  display: flex;
  flex-direction: column;
  gap: 10px;
  flex: 1;
}

.option-input {
  width: 100%;
}

.delete-option-btn {
  flex-shrink: 0;
  min-width: 70px;
  margin-top: 10px;
}

.add-option-btn {
  margin-top: 8px;
  width: 100%;
}
</style>
