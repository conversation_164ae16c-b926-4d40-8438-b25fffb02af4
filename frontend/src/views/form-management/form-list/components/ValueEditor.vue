<template>
  <div class="value-editor">
    <el-select v-model="valueType" style="width:100px;margin-right:8px;" @change="onValueTypeChange">
      <el-option label="字段" value="property" />
      <el-option label="常量" value="const" />
      <el-option label="函数" value="func" />
    </el-select>

    <!-- 字段选择 -->
    <template v-if="valueType === 'property'">
      <div class="property-editor-container">
        <div class="property-main-field">
          <el-select
            :model-value="propertyField"
            placeholder="选择字段"
            style="width:140px"
            filterable
            @update:model-value="onPropertyFieldChange"
          >
            <el-option v-for="f in allFields" :key="f.code" :label="f.labelZh || f.code" :value="f.code" />
          </el-select>

          <el-select
            :model-value="propertyType"
            placeholder="类型"
            style="width:100px;margin-left:8px"
            @update:model-value="onPropertyTypeChange"
          >
            <el-option label="字符串" value="string" />
            <el-option label="整数" value="int" />
            <el-option label="浮点" value="float" />
            <el-option label="日期" value="date" />
            <el-option label="布尔" value="boolean" />
          </el-select>
        </div>

        <!-- 多级属性 -->
        <div v-if="propertyPath.length > 1" class="property-levels">
          <div class="property-levels-header">
            <span class="property-levels-title">属性层级</span>
          </div>
          <div v-for="(prop, idx) in propertyPath.slice(1)" :key="idx + 1" class="property-level-item">
            <span class="property-level-label">第{{ idx + 2 }}级:</span>
            <el-input
              :model-value="propertyPath[idx + 1]"
              placeholder="属性名"
              style="width:100px;"
              @update:model-value="(val) => onPropertyPathChange(idx + 1, val)"
            />
            <el-button
              size="mini"
              type="danger"
              @click="removePropertyLevel(idx + 1)"
              style="margin-left:4px"
            >删除</el-button>
          </div>
        </div>

        <div class="property-actions">
          <el-button size="mini" type="primary" @click="addPropertyLevel">
            <i class="el-icon-plus"></i> 添加属性层级
          </el-button>
        </div>
      </div>
    </template>

    <!-- 常量输入 -->
    <template v-else-if="valueType === 'const'">
      <el-input
        :model-value="constValue"
        placeholder="常量值"
        style="width:120px"
        @update:model-value="onConstValueChange"
      />
      <el-select
        :model-value="constType"
        placeholder="类型"
        style="width:80px;margin-left:4px"
        @update:model-value="onConstTypeChange"
      >
        <el-option label="文本" value="string" />
        <el-option label="数字" value="number" />
        <el-option label="日期" value="date" />
        <el-option label="布尔" value="boolean" />
      </el-select>
    </template>

    <!-- 函数选择 -->
    <template v-else-if="valueType === 'func'">
      <div class="func-editor-container">
        <div class="func-main">
          <el-select
            :model-value="funcName"
            placeholder="函数名"
            style="width:120px"
            @update:model-value="onFuncNameChange"
          >
            <el-option v-for="fn in funcNameOptions" :key="fn" :label="fn" :value="fn" />
          </el-select>
        </div>

        <!-- 函数参数 -->
        <div v-if="funcArgs.length > 0" class="func-args-container">
          <div class="func-args-header">
            <span class="func-args-title">函数参数</span>
          </div>
          <div v-for="(arg, idx) in funcArgs" :key="idx" class="func-arg-item">
            <div class="func-arg-label">参数{{ idx + 1 }}:</div>
            <div class="func-arg-content">
              <ValueEditor
                :model-value="funcArgs[idx]"
                :all-fields="allFields"
                @update:model-value="(val) => onFuncArgChange(idx, val)"
              />
            </div>
            <el-button
              size="mini"
              type="danger"
              @click="removeFuncArg(idx)"
              style="margin-left:8px;"
            >删除</el-button>
          </div>
        </div>

        <div class="func-actions">
          <el-button size="mini" type="primary" @click="addFuncArg">
            <i class="el-icon-plus"></i> 添加参数
          </el-button>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { dslFuncs } from '@/utils/dsl-funcs';

const props = defineProps({
  modelValue: { type: Object, required: true },
  allFields: { type: Array, default: () => [] }
});
const emit = defineEmits(['update:modelValue']);

const funcNameOptions = computed(() => Object.keys(dslFuncs));

// 值类型识别
const valueType = ref('const');
watch(() => props.modelValue, (v) => {
  if (!v) {
    valueType.value = 'const';
    return;
  }

  if (v.property !== undefined) valueType.value = 'property';
  else if (v.const !== undefined) valueType.value = 'const';
  else if (v.func !== undefined) valueType.value = 'func';
  else {
    // 如果都没有，默认为常量
    valueType.value = 'const';
  }
}, { immediate: true, deep: true });

// 安全的 modelValue 访问
const safeModelValue = computed(() => props.modelValue || {});

function onValueTypeChange() {
  let newVal;
  if (valueType.value === 'property') {
    newVal = { property: [''], type: 'string' };
  } else if (valueType.value === 'const') {
    newVal = { const: '', type: 'string' };
  } else if (valueType.value === 'func') {
    newVal = { func: funcNameOptions.value[0] || 'now', args: [], type: 'string' };
  }
  emit('update:modelValue', newVal);
}

// 属性处理
const propertyField = computed(() => {
  return safeModelValue.value.property?.[0] || '';
});

const propertyPath = computed(() => {
  return safeModelValue.value.property || [''];
});

const propertyType = computed(() => {
  return safeModelValue.value.type || 'string';
});

function updateProperty(path, type = propertyType.value) {
  emit('update:modelValue', {
    property: path,
    type: type
  });
}

function onPropertyFieldChange(val) {
  const newPath = [val, ...propertyPath.value.slice(1)];
  updateProperty(newPath);
}

function onPropertyPathChange(idx, val) {
  const newPath = [...propertyPath.value];
  newPath[idx] = val;
  updateProperty(newPath);
}

function onPropertyTypeChange(val) {
  updateProperty(propertyPath.value, val);
}

function addPropertyLevel() {
  updateProperty([...propertyPath.value, '']);
}

function removePropertyLevel(idx) {
  if (propertyPath.value.length > 1) {
    const newPath = [...propertyPath.value];
    newPath.splice(idx, 1);
    updateProperty(newPath);
  }
}

// 常量处理
const constValue = computed(() => {
  const val = safeModelValue.value;

  // 直接检查 const 字段
  if (val.const !== undefined) {
    return String(val.const);
  }

  // 如果是原始值（字符串、数字、布尔值）
  if (typeof val === 'string' || typeof val === 'number' || typeof val === 'boolean') {
    return String(val);
  }

  // 如果有 value 字段
  if (val.value !== undefined) {
    return String(val.value);
  }

  return '';
});

const constType = computed(() => {
  const val = safeModelValue.value;

  // 直接检查 type 字段
  if (val.type) {
    return val.type;
  }

  // 根据 const 值推断类型
  if (val.const !== undefined) {
    if (typeof val.const === 'number') return 'number';
    if (typeof val.const === 'boolean') return 'boolean';
    return 'string';
  }

  // 如果是原始值，根据类型推断
  if (typeof val === 'number') return 'number';
  if (typeof val === 'boolean') return 'boolean';

  return 'string';
});

function updateConst(value, type) {
  // 根据类型转换值
  let convertedValue = value;

  if (type === 'boolean') {
    // 布尔类型转换
    if (typeof value === 'string') {
      convertedValue = value.toLowerCase() === 'true';
    } else {
      convertedValue = Boolean(value);
    }
  } else if (type === 'number') {
    // 数字类型转换
    if (typeof value === 'string') {
      const num = Number(value);
      convertedValue = isNaN(num) ? 0 : num;
    } else {
      convertedValue = Number(value);
    }
  } else if (type === 'integer') {
    // 整数类型转换
    if (typeof value === 'string') {
      const num = parseInt(value, 10);
      convertedValue = isNaN(num) ? 0 : num;
    } else {
      convertedValue = parseInt(value, 10);
    }
  } else {
    // 字符串和其他类型保持字符串
    convertedValue = String(value);
  }

  emit('update:modelValue', { const: convertedValue, type: type });
}

function onConstValueChange(val) {
  updateConst(val, constType.value);
}

function onConstTypeChange(val) {
  // 当类型改变时，需要重新转换当前值
  updateConst(constValue.value, val);
}

// 函数处理
const funcName = computed(() => {
  return safeModelValue.value.func || '';
});

const funcArgs = computed(() => {
  return safeModelValue.value.args || [];
});

function updateFunc(name, args) {
  emit('update:modelValue', {
    func: name,
    args: args,
    type: safeModelValue.value.type || 'string'
  });
}

function onFuncNameChange(val) {
  updateFunc(val, funcArgs.value);
}

function onFuncArgChange(idx, val) {
  const newArgs = [...funcArgs.value];
  newArgs[idx] = val;
  updateFunc(funcName.value, newArgs);
}

function addFuncArg() {
  const newArgs = [...funcArgs.value, { const: '', type: 'string' }];
  updateFunc(funcName.value, newArgs);
}

function removeFuncArg(idx) {
  const newArgs = [...funcArgs.value];
  newArgs.splice(idx, 1);
  updateFunc(funcName.value, newArgs);
}
</script>

<style scoped>
.value-editor {
  display: flex;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 4px;
}

/* 属性编辑器样式 */
.property-editor-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 300px;
}

.property-main-field {
  display: flex;
  align-items: center;
  gap: 8px;
}

.property-levels {
  background: #f8fafc;
  border: 1px solid #e3e6ea;
  border-radius: 6px;
  padding: 8px;
  margin-left: 12px;
}

.property-levels-header {
  margin-bottom: 8px;
}

.property-levels-title {
  font-size: 12px;
  font-weight: 600;
  color: #666;
}

.property-level-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
  padding: 6px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #e3e6ea;
}

.property-level-item:last-child {
  margin-bottom: 0;
}

.property-level-label {
  font-size: 12px;
  color: #666;
  min-width: 50px;
}

.property-actions {
  margin-left: 12px;
}

/* 函数编辑器样式 */
.func-editor-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 300px;
}

.func-main {
  display: flex;
  align-items: center;
  gap: 8px;
}

.func-args-container {
  background: #f8fafc;
  border: 1px solid #e3e6ea;
  border-radius: 6px;
  padding: 8px;
  margin-left: 12px;
}

.func-args-header {
  margin-bottom: 8px;
}

.func-args-title {
  font-size: 12px;
  font-weight: 600;
  color: #666;
}

.func-arg-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 8px;
  padding: 8px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #e3e6ea;
}

.func-arg-item:last-child {
  margin-bottom: 0;
}

.func-arg-label {
  font-size: 12px;
  color: #666;
  min-width: 50px;
  margin-top: 8px;
}

.func-arg-content {
  flex: 1;
}

.func-actions {
  margin-left: 12px;
}

/* 常量编辑器样式 */
.const-editor {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>