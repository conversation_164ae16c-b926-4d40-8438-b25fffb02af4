<template>
  <div class="value-editor">
    <el-select v-model="valueType" style="width:120px;margin-right:8px;" @change="onValueTypeChange">
      <el-option label="字段" value="property" />
      <el-option label="常量" value="const" />
      <el-option label="函数" value="func" />
    </el-select>

    <!-- 字段选择 -->
    <div v-if="valueType === 'property'" class="property-editor">
      <el-select
        v-model="propertyField"
        placeholder="选择字段"
        style="width:140px"
        filterable
        @change="onPropertyChange"
      >
        <el-option v-for="f in allFields" :key="f.code" :label="f.labelZh || f.code" :value="f.code" />
      </el-select>
      
      <!-- 多级属性 -->
      <div v-for="(prop, idx) in propertyPath.slice(1)" :key="idx + 1" class="property-level">
        <el-input
          v-model="propertyPath[idx + 1]"
          placeholder="属性名"
          style="width:100px;margin-left:4px;"
          @input="onPropertyChange"
        />
        <el-button
          size="mini"
          type="danger"
          @click="removePropertyLevel(idx + 1)"
          style="margin-left:4px"
        >删除</el-button>
      </div>
      
      <el-button size="mini" @click="addPropertyLevel" style="margin-left:4px;">添加层级</el-button>
      
      <el-select v-model="propertyType" placeholder="类型" style="width:100px;margin-left:8px" @change="onPropertyChange">
        <el-option label="字符串" value="string" />
        <el-option label="整数" value="int" />
        <el-option label="浮点" value="float" />
        <el-option label="日期" value="date" />
        <el-option label="布尔" value="boolean" />
      </el-select>
    </div>

    <!-- 常量输入 -->
    <div v-else-if="valueType === 'const'" class="const-editor">
      <el-input v-model="constValue" placeholder="常量值" style="width:120px" @input="onConstChange" />
      <el-select v-model="constType" placeholder="类型" style="width:100px;margin-left:8px" @change="onConstChange">
        <el-option label="字符串" value="string" />
        <el-option label="整数" value="int" />
        <el-option label="浮点" value="float" />
        <el-option label="日期" value="date" />
        <el-option label="布尔" value="boolean" />
      </el-select>
    </div>

    <!-- 函数选择 -->
    <div v-else-if="valueType === 'func'" class="func-editor">
      <el-select v-model="funcName" placeholder="函数名" style="width:120px" @change="onFuncChange">
        <el-option v-for="fn in funcNameOptions" :key="fn" :label="fn" :value="fn" />
      </el-select>
      <el-select v-model="funcType" placeholder="返回类型" style="width:100px;margin-left:8px" @change="onFuncChange">
        <el-option label="字符串" value="string" />
        <el-option label="整数" value="int" />
        <el-option label="浮点" value="float" />
        <el-option label="日期" value="date" />
        <el-option label="布尔" value="boolean" />
      </el-select>
      
      <!-- 函数参数 -->
      <div v-for="(arg, idx) in funcArgs" :key="idx" class="func-arg">
        <ValueEditor v-model="funcArgs[idx]" :all-fields="allFields" />
        <el-button size="mini" @click="removeFuncArg(idx)" style="margin-left:4px;">删除参数</el-button>
      </div>
      <el-button size="small" @click="addFuncArg" style="margin-top:4px;">添加参数</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { dslFuncs } from '@/utils/dsl-funcs';

const props = defineProps({
  modelValue: { type: Object, required: true },
  allFields: { type: Array, default: () => [] }
});
const emit = defineEmits(['update:modelValue']);

const funcNameOptions = computed(() => Object.keys(dslFuncs));

// 值类型识别
const valueType = ref('const');
watch(() => props.modelValue, (v) => {
  if (!v) return;
  if (v.property !== undefined) valueType.value = 'property';
  else if (v.const !== undefined) valueType.value = 'const';
  else if (v.func !== undefined) valueType.value = 'func';
}, { immediate: true, deep: true });

function onValueTypeChange() {
  let newVal;
  if (valueType.value === 'property') {
    newVal = { property: [''], type: 'string' };
  } else if (valueType.value === 'const') {
    newVal = { const: '', type: 'string' };
  } else if (valueType.value === 'func') {
    newVal = { func: funcNameOptions.value[0] || '', args: [], type: 'string' };
  }
  emit('update:modelValue', newVal);
}

// 属性处理
const propertyField = computed({
  get() { return props.modelValue.property?.[0] || ''; },
  set(val) { updateProperty([val, ...propertyPath.value.slice(1)]); }
});

const propertyPath = computed({
  get() { return props.modelValue.property || ['']; },
  set(val) { updateProperty(val); }
});

const propertyType = computed({
  get() { return props.modelValue.type || 'string'; },
  set(val) { updateProperty(propertyPath.value, val); }
});

function updateProperty(path, type = propertyType.value) {
  emit('update:modelValue', {
    property: path,
    type: type
  });
}

function onPropertyChange() {
  updateProperty(propertyPath.value, propertyType.value);
}

function addPropertyLevel() {
  updateProperty([...propertyPath.value, '']);
}

function removePropertyLevel(idx) {
  if (propertyPath.value.length > 1) {
    const newPath = [...propertyPath.value];
    newPath.splice(idx, 1);
    updateProperty(newPath);
  }
}

// 常量处理
const constValue = computed({
  get() { return props.modelValue.const || ''; },
  set(val) { updateConst(val, constType.value); }
});

const constType = computed({
  get() { return props.modelValue.type || 'string'; },
  set(val) { updateConst(constValue.value, val); }
});

function updateConst(value, type) {
  emit('update:modelValue', { const: value, type: type });
}

function onConstChange() {
  updateConst(constValue.value, constType.value);
}

// 函数处理
const funcName = computed({
  get() { return props.modelValue.func || ''; },
  set(val) { updateFunc(val, funcType.value, funcArgs.value); }
});

const funcType = computed({
  get() { return props.modelValue.type || 'string'; },
  set(val) { updateFunc(funcName.value, val, funcArgs.value); }
});

const funcArgs = computed({
  get() { return props.modelValue.args || []; },
  set(val) { updateFunc(funcName.value, funcType.value, val); }
});

function updateFunc(name, type, args) {
  emit('update:modelValue', { func: name, type: type, args: args });
}

function onFuncChange() {
  updateFunc(funcName.value, funcType.value, funcArgs.value);
}

function addFuncArg() {
  const newArgs = [...funcArgs.value, { const: '', type: 'string' }];
  updateFunc(funcName.value, funcType.value, newArgs);
}

function removeFuncArg(idx) {
  const newArgs = [...funcArgs.value];
  newArgs.splice(idx, 1);
  updateFunc(funcName.value, funcType.value, newArgs);
}
</script>

<style scoped>
.value-editor {
  display: flex;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 4px;
}
.property-editor, .const-editor, .func-editor {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 4px;
}
.property-level {
  display: flex;
  align-items: center;
}
.func-arg {
  display: flex;
  align-items: center;
  margin-top: 4px;
  padding: 4px;
  border: 1px dashed #ddd;
  border-radius: 4px;
}
</style>
