<template>
  <div class="value-editor">
    <el-select v-model="valueType" style="width:100px;margin-right:8px;" @change="onValueTypeChange">
      <el-option label="字段" value="property" />
      <el-option label="常量" value="const" />
      <el-option label="函数" value="func" />
    </el-select>

    <!-- 字段选择 -->
    <template v-if="valueType === 'property'">
      <el-select
        :model-value="propertyField"
        placeholder="选择字段"
        style="width:140px"
        filterable
        @update:model-value="onPropertyFieldChange"
      >
        <el-option v-for="f in allFields" :key="f.code" :label="f.labelZh || f.code" :value="f.code" />
      </el-select>

      <!-- 多级属性 -->
      <template v-for="(prop, idx) in propertyPath.slice(1)" :key="idx + 1">
        <el-input
          :model-value="propertyPath[idx + 1]"
          placeholder="属性名"
          style="width:80px;margin-left:4px;"
          @update:model-value="(val) => onPropertyPathChange(idx + 1, val)"
        />
        <el-button
          size="mini"
          type="danger"
          @click="removePropertyLevel(idx + 1)"
          style="margin-left:2px"
        >×</el-button>
      </template>

      <el-button size="mini" @click="addPropertyLevel" style="margin-left:4px;">+</el-button>

      <el-select
        :model-value="propertyType"
        placeholder="类型"
        style="width:100px;margin-left:8px"
        @update:model-value="onPropertyTypeChange"
      >
        <el-option label="字符串" value="string" />
        <el-option label="整数" value="int" />
        <el-option label="浮点" value="float" />
        <el-option label="日期" value="date" />
        <el-option label="布尔" value="boolean" />
      </el-select>
    </template>

    <!-- 常量输入 -->
    <template v-else-if="valueType === 'const'">
      <el-input v-model="constValue" placeholder="常量值" style="width:120px" @input="onConstChange" />
      <el-select v-model="constType" placeholder="类型" style="width:80px;margin-left:4px" @change="onConstChange">
        <el-option label="文本" value="string" />
        <el-option label="数字" value="number" />
        <el-option label="日期" value="date" />
        <el-option label="布尔" value="boolean" />
      </el-select>
    </template>

    <!-- 函数选择 -->
    <template v-else-if="valueType === 'func'">
      <el-select v-model="funcName" placeholder="函数名" style="width:100px" @change="onFuncChange">
        <el-option v-for="fn in funcNameOptions" :key="fn" :label="fn" :value="fn" />
      </el-select>

      <!-- 函数参数 -->
      <template v-if="funcArgs.length > 0">
        <div class="func-args">
          <div v-for="(arg, idx) in funcArgs" :key="idx" class="func-arg">
            <ValueEditor v-model="funcArgs[idx]" :all-fields="allFields" />
            <el-button size="mini" @click="removeFuncArg(idx)" style="margin-left:4px;">×</el-button>
          </div>
        </div>
      </template>
      <el-button size="mini" @click="addFuncArg" style="margin-left:4px;">+参数</el-button>
    </template>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { dslFuncs } from '@/utils/dsl-funcs';

const props = defineProps({
  modelValue: { type: Object, required: true },
  allFields: { type: Array, default: () => [] }
});
const emit = defineEmits(['update:modelValue']);

const funcNameOptions = computed(() => Object.keys(dslFuncs));

// 值类型识别
const valueType = ref('const');
watch(() => props.modelValue, (v) => {
  if (!v) return;
  if (v.property !== undefined) valueType.value = 'property';
  else if (v.const !== undefined) valueType.value = 'const';
  else if (v.func !== undefined) valueType.value = 'func';
}, { immediate: true, deep: true });

// 安全的 modelValue 访问
const safeModelValue = computed(() => props.modelValue || {});

function onValueTypeChange() {
  let newVal;
  if (valueType.value === 'property') {
    newVal = { property: [''], type: 'string' };
  } else if (valueType.value === 'const') {
    newVal = { const: '', type: 'string' };
  } else if (valueType.value === 'func') {
    newVal = { func: funcNameOptions.value[0] || 'now', args: [], type: 'string' };
  }
  emit('update:modelValue', newVal);
}

// 属性处理
const propertyField = computed({
  get() { return safeModelValue.value.property?.[0] || ''; },
  set(val) {
    const newPath = [val, ...propertyPath.value.slice(1)];
    updateProperty(newPath);
  }
});

const propertyPath = computed({
  get() { return safeModelValue.value.property || ['']; },
  set(val) { updateProperty(val); }
});

function updateProperty(path) {
  emit('update:modelValue', {
    property: path,
    type: safeModelValue.value.type || 'string'
  });
}

function onPropertyChange() {
  updateProperty(propertyPath.value);
}

function addPropertyLevel() {
  updateProperty([...propertyPath.value, '']);
}

function removePropertyLevel(idx) {
  if (propertyPath.value.length > 1) {
    const newPath = [...propertyPath.value];
    newPath.splice(idx, 1);
    updateProperty(newPath);
  }
}

// 常量处理
const constValue = computed({
  get() { return safeModelValue.value.const || ''; },
  set(val) { updateConst(val, constType.value); }
});

const constType = computed({
  get() { return safeModelValue.value.type || 'string'; },
  set(val) { updateConst(constValue.value, val); }
});

function updateConst(value, type) {
  emit('update:modelValue', { const: value, type: type });
}

function onConstChange() {
  updateConst(constValue.value, constType.value);
}

// 函数处理
const funcName = computed({
  get() { return safeModelValue.value.func || ''; },
  set(val) { updateFunc(val, funcArgs.value); }
});

const funcArgs = computed({
  get() { return safeModelValue.value.args || []; },
  set(val) { updateFunc(funcName.value, val); }
});

function updateFunc(name, args) {
  emit('update:modelValue', {
    func: name,
    args: args,
    type: safeModelValue.value.type || 'string'
  });
}

function onFuncChange() {
  updateFunc(funcName.value, funcArgs.value);
}

function addFuncArg() {
  const newArgs = [...funcArgs.value, { const: '', type: 'string' }];
  updateFunc(funcName.value, newArgs);
}

function removeFuncArg(idx) {
  const newArgs = [...funcArgs.value];
  newArgs.splice(idx, 1);
  updateFunc(funcName.value, newArgs);
}
</script>

<style scoped>
.value-editor {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 4px;
}
.func-args {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-left: 8px;
}
.func-arg {
  display: flex;
  align-items: center;
  padding: 4px;
  border: 1px dashed #ddd;
  border-radius: 4px;
  background: #fafafa;
}
</style>