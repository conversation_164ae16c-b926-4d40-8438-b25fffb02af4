<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="login-bg">
    <div class="login-left">
      <div class="login-form-area">
        <img class="login-logo" src="/img/login01.png" alt="logo" />
        <div class="login-form-content">
          <div class="title-container">
            <h3 class="title">{{ $t("login.title") }}</h3>
            <lang-select class="lang-select" />
          </div>

          <el-form
            ref="loginFormRef"
            :model="loginForm"
            :rules="loginRules"
            class="login-form"
            autocomplete="on"
            label-position="left"
          >
            <el-form-item prop="username">
              <el-input
                v-model="loginForm.username"
                :placeholder="$t('login.username')"
                type="text"
                tabindex="1"
                autocomplete="on"
              >
                <template #prefix>
                  <el-icon>
                    <User />
                  </el-icon>
                </template>
              </el-input>
            </el-form-item>

            <el-form-item prop="password">
              <el-input
                v-model="loginForm.password"
                :type="passwordVisible ? 'text' : 'password'"
                :placeholder="$t('login.password')"
                tabindex="2"
                autocomplete="on"
                @keyup.enter="handleLogin"
              >
                <template #prefix>
                  <el-icon>
                    <Lock />
                  </el-icon>
                </template>
                <template #suffix>
                  <el-icon
                    class="show-pwd"
                    @click="passwordVisible = !passwordVisible"
                  >
                    <View v-if="passwordVisible" />
                    <Hide v-else />
                  </el-icon>
                </template>
              </el-input>
            </el-form-item>

            <el-form-item prop="captcha">
              <div class="captcha-container">
                <el-input
                  v-model="loginForm.captchaCode"
                  :placeholder="$t('login.captcha')"
                  tabindex="3"
                  class="captcha-input"
                  @keyup.enter="handleLogin"
                >
                  <template #prefix>
                    <el-icon>
                      <Picture />
                    </el-icon>
                  </template>
                </el-input>
                <div class="captcha-image" @click="refreshCaptcha">
                  <img v-if="captchaImage" :src="captchaImage" alt="验证码" />
                  <div v-else class="captcha-loading">
                    <el-icon>
                      <Loading />
                    </el-icon>
                  </div>
                </div>
              </div>
            </el-form-item>

            <el-button
              :loading="loading"
              type="primary"
              class="login-button"
              @click="handleLogin"
            >
              <el-icon>
                <Histogram />
              </el-icon>
              {{ $t("login.login") }}
            </el-button>

            <div class="login-options">
              <router-link to="/forgot-password" class="forgot-password">
                {{ $t("login.forgotPassword") }}
              </router-link>
              <router-link
                to="/register"
                class="forgot-password"
                style="margin-left: 50px"
              >
                {{ $t("login.register") }}
              </router-link>
            </div>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useUserStore } from "@/stores/user";
import {
  User,
  Lock,
  View,
  Hide,
  Picture,
  Loading,
  Histogram,
} from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import type { FormInstance, FormRules } from "element-plus";
import LangSelect from "@/components/common/LangSelect.vue";
import { getDeviceId, getDeviceInfoString } from "@/utils/device";

const router = useRouter();
const route = useRoute();
const userStore = useUserStore();
const loginFormRef = ref<FormInstance>();
const loading = ref(false);
const passwordVisible = ref(false);
const captchaImage = ref<string>("");

const loginForm = reactive({
  username: "",
  password: "",
  captchaId: "",
  captchaCode: "",
});

const loginRules: FormRules = {
  username: [{ required: true, trigger: "blur", message: "请输入用户名" }],
  password: [{ required: true, trigger: "blur", message: "请输入密码" }],
  captchaCode: [{ required: true, trigger: "blur", message: "请输入验证码" }],
};

const refreshCaptcha = async () => {
  try {
    const captcha = await userStore.getCaptcha();
    if (captcha) {
      captchaImage.value = captcha.image;
      loginForm.captchaId = captcha.id;
    }
  } catch (error) {
    console.error("Failed to get captcha:", error);
    ElMessage.error(
      '获取验证码失败，请刷新页面重试/Failed to get the verification code. Please refresh the page and try again."'
    );
  }
};

const handleLogin = async () => {
  if (!loginFormRef.value) return;

  try {
    loading.value = true;
    await loginFormRef.value.validate();

    const loginData = {
      username: loginForm.username,
      password: loginForm.password,
      captchaId: loginForm.captchaId,
      captchaCode: loginForm.captchaCode,
      clientIp: "",
      deviceInfo: getDeviceInfoString(),
      deviceId: getDeviceId(),
    };

    const result = await userStore.login(loginData);

    if (result.accessToken) {
      const redirect = route.query.redirect as string;
      try {
        if (redirect) {
          await router.push({ path: redirect });
        } else {
          await router.push({ path: "dashboard" });
        }
      } catch (error) {
        if (redirect) {
          window.location.href = redirect;
        } else {
          window.location.href = "dashboard";
        }
      }
    } else {
      ElMessage.error(
        "登录失败，请检查用户名和密码/Login failed, please check your username and password."
      );
      await refreshCaptcha();
    }
  } catch (error: any) {
    console.error("Login failed:", error);
    ElMessage.error(
      error.message || "登录失败/Login failed, please try again."
    );
    await refreshCaptcha();
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  refreshCaptcha();
});
</script>

<style lang="scss" scoped>
.login-bg {
  min-height: 100vh;
  width: 100vw;
  background: url("/img/login_bg.png") no-repeat center center;
  background-size: cover;
  display: flex;
  align-items: center;
}

.login-left {
  flex: 0 0 480px;
  display: flex;
  align-items: center;
  height: 100vh;
  justify-content: flex-start;
}

.login-form-area {
  position: relative;
  width: 700px;
  height: 420px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  padding: 40px 32px 32px 32px;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-left: 100px;
  background-image: url("/img/login_bg1.png");
}

.login-logo {
  position: absolute;
  left: 20%;
  top: -85px;
  transform: translateX(-50%);
  height: auto;
  z-index: 2;
  background: transparent;
  pointer-events: none;
}

.login-form-content {
}

.title-container {
  position: relative;
  margin-bottom: 30px;
  text-align: center;

  .title {
    font-size: 26px;
    color: #fff;
    margin: 0 auto 25px auto;
    font-weight: bold;
    line-height: 26px;
  }

  .lang-select {
    position: absolute;
    top: 9px;
    right: 0;
    color: #fff;
    cursor: pointer;
  }
}

:deep(.el-input) {
  height: 48px;
  width: 100% !important;

  input {
    height: 48px;
  }

  .el-input__wrapper {
    width: 100%;
  }

  .el-input__prefix {
    display: flex;
    align-items: center;
    color: #909399;
  }
}

.show-pwd {
  cursor: pointer;
  color: #909399;
}

.captcha-container {
  display: flex;
  align-items: center;
  width: 100%;

  .captcha-input {
    flex: 1;
    width: auto !important;

    :deep(.el-input__wrapper) {
      width: 100%;
    }
  }

  .captcha-image {
    width: 120px;
    height: 48px;
    margin-left: 10px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    overflow: hidden;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .captcha-loading {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      width: 100%;
      color: #909399;
      font-size: 24px;

      .el-icon {
        animation: rotating 2s linear infinite;
      }
    }
  }
}

.login-button {
  width: 100%;
  height: 48px;
  margin-top: 10px;
}

.login-options {
  margin-top: 15px;
  text-align: center;

  .forgot-password {
    color: #fff;
    font-size: 14px;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
