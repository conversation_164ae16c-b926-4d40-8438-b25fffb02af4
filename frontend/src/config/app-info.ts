import { AppInfo } from "@/dtos";
// 静态应用信息集合
export const appInfoList: AppInfo[] = [
  {
    appCode: 'rbac-mgt',
    appName: 'RBAC管理站点',
    proxyApiUrl: '/api/rbac-mgt'
  },
  {
    appCode: 'rbac',
    appName: 'RBAC认证站点',
    proxyApiUrl: '/api/rbac'
  },
  {
    appCode: 'files',
    appName: '文件站点',
    proxyApiUrl: '/api/files'
  },
  {
    appCode: 'files-mgt',
    appName: '文件管理站点',
    proxyApiUrl: '/api/files-mgt'
  },
  {
    appCode: 'dynamic-form',
    appName: '动态表单站点',
    proxyApiUrl: '/api/dynamic-form'
  },
  {
    appCode: 'dynamic-form-mgt',
    appName: '动态表单管理站点',
    proxyApiUrl: '/api/dynamic-form-mgt'
  },
  {
    appCode: 'itmctr',
    appName: 'ITMCTR站点',
    proxyApiUrl: '/api/itmctr'
  },
  {
    appCode: 'itmctr-mgt',
    appName: 'ITMCTR管理站点',
    proxyApiUrl: '/api/itmctr-mgt'
  },
];


// 获取应用信息
export const getAppInfo = (appCode: string): AppInfo | undefined => {
  return appInfoList.find(app => app.appCode === appCode);
};

export const getAppInfoList = (): AppInfo[] => {
  return appInfoList.map(app => ({
    ...app
  }))
}
