<template>
  <div>
    <BaseField
      :field="field"
      :value="value"
      :previousValue="previousValue"
      :language="language"
      :formData="formData"
      :rowIndex="rowIndex"
      :annotation="annotation"
      :renderMode="renderMode"
      :index="index"
      :total="total"
      default-label="单选框"
      @edit-field="(...args) => $emit('edit-field', ...args)"
      @delete-field="(...args) => $emit('delete-field', ...args)"
      @move-down="(...args) => $emit('move-down', ...args)"
      @move-up="(...args) => $emit('move-up', ...args)"
    >
      <template #edit="{ readonly }">
        <el-radio-group v-model="modelValue" :disabled="readonly" style="flex: 1">
          <el-radio
              v-for="opt in field.options || []"
              :key="opt.value"
              :label="opt.value"
          >
            {{
              language === 'both'
                  ? (opt.labelZh || opt.label || opt.value) + '/' + (opt.labelEn || opt.value)
                  : (language === 'zh'
                      ? (opt.labelZh || opt.label || opt.value)
                      : (opt.labelEn || opt.value))
            }}
          </el-radio>
        </el-radio-group>
        <div class="field-content" v-if="annotation?.fill">
          <el-text type="primary"> ⬆️AI提取内容：{{ annotation?.fill }}</el-text>
        </div>
      </template>
      <template #view>
        <div class="form-view-value">
          {{ getDisplay(modelValue, field) }}
        </div>
      </template>
    </BaseField>
  </div>
</template>

<script setup lang="ts">
import {ElRadioGroup, ElRadio} from "element-plus";
import {BaseField, RenderMode} from "../index";
import {getDisplay} from "./display";
import { ref, watch } from "vue";
import { validate as logicValidate } from "./logic"; // 你的校验逻辑


const props = defineProps({
  field: {type: Object, required: true},
  value: {type: [String, Number], default: ""},
  previousValue: {type: [String, Number], default: ""},
  annotation: {type: Object, required: false},
  renderMode: {
    type: String as () => RenderMode,
    default: "edit",
  },
  index: { type: Number },
  total: { type: Number },
  rowIndex: {
    type: Number,
    required: false,
  },
  language: {type: String as () => "zh" | "en" | "both", default: "zh"},
  formData: {
    type: Object as PropType<Record<string, any>>,
    required: true,
  },
});

const emit = defineEmits(["update:value"]);

// 创建本地响应式数据
const modelValue = ref(props.value);

// 监听外部值变化
watch(
    () => props.value,
    (newVal) => {
      modelValue.value = newVal;
    }
);

// 监听本地值变化，向上传递
watch(
    () => modelValue.value,
    (newVal) => {
      emit("update:value", newVal);
    }
);

</script>

<style scoped>
/* 组件特定样式 */
</style>
