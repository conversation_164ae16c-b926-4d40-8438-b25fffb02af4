<template>
  <div>
    <BaseField
      :field="field"
      :value="value"
      :previousValue="previousValue"
      :rowIndex="rowIndex"
      :language="language"
      :formData="formData"
      :annotation="annotation"
      :renderMode="renderMode"
      default-label="日期"
      :index="index"
      :total="total"
      @edit-field="(...args) => $emit('edit-field', ...args)"
      @delete-field="(...args) => $emit('delete-field', ...args)"
      @move-down="(...args) => $emit('move-down', ...args)"
      @move-up="(...args) => $emit('move-up', ...args)"
    >
      <template #edit="{ readonly }">
        <el-date-picker
            v-model="modelValue"
            type="date"
            :placeholder="placeholder"
            :disabled="readonly"
            :format="field.extends.format"
            :value-format="field.extends.format"
            style="flex: 1"
            clearable
        />
        <div class="field-content" v-if="annotation?.fill">
          <el-text type="primary"> ⬆️AI提取内容：{{ annotation?.fill }}</el-text>
        </div>
      </template>
      <template #view>
        <div class="form-view-value">
          {{ getDisplay(modelValue, field as FieldDto) }}
        </div>
      </template>
    </BaseField>
  </div>
</template>

<script setup lang="ts">
import {ElDatePicker} from "element-plus";
import {BaseField, RenderMode} from "../index";
import {getDisplay} from "./display";
import {FieldDto} from "@/dtos/dynamic-form.dto";
import {ref, watch, computed} from "vue";
import {validate as logicValidate} from "./logic"; // 你的校验逻辑

const props = defineProps({
  field: {type: Object, required: true},
  value: {type: String, default: () => ""},
  previousValue: {type: String, default: () => ""},
  annotation: {type: Object, required: false},
  rowIndex: {type: Number, required: false},
  index: { type: Number },
  total: { type: Number },
  renderMode: {
    type: String as () => RenderMode,
    default: "edit",
  },
  language: {type: String as () => "zh" | "en" | "both", default: "zh"},
  formData: {
    type: Object as PropType<Record<string, any>>,
    required: true,
  },
});

const emit = defineEmits(["update:value"]);
// 创建本地响应式数据
const modelValue = ref(props.value);
const placeholder = computed(() => {
  if (props.language == "zh") {
    return "请选择日期";
  } else if (props.language == "en") {
    return "Please select the date";
  } else {
    return "请选择日期/Please select the date";
  }
});
// 监听外部值变化
watch(
    () => props.value,
    (newVal) => {
      modelValue.value = newVal;
    }
);

// 监听本地值变化，向上传递
watch(
    () => modelValue.value,
    (newVal) => {
      emit("update:value", newVal);
    }
);

</script>

<style scoped>
/* 组件特定样式 */
</style>
