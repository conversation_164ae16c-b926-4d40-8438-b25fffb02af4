<template>
  <el-dialog
      :model-value="visible"
      @update:model-value="emit('cancel')"
      title="批注审核信息"
      width="700px"
      :close-on-click-modal="false"
      :show-close="true"
      append-to-body
      style="z-index: 3000 !important"
      destroy-on-close
      class="field-annotation-dialog"
  >
    <el-form label-width="150px" class="annotation-form" label-position="right">
      <el-form-item label="当前批注字段">
        <el-input v-model="field.labelZh" :disabled="true"/>
      </el-form-item>
      <el-form-item v-if="field?.rejectReasons" label="请选择驳回原因">
        <el-select
            v-model="selectedReason"
            filterable
            allow-create
            placeholder="请选择或输入驳回原因"
            @change="handleReasonChange"
        >
          <el-option
              v-for="item in field?.rejectReasons || []"
              :key="item"
              :label="item"
              :value="item"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="请填写驳回原因">
        <el-input
            v-model="textareaReason"
            type="textarea"
            :rows="5"
            placeholder="请填写驳回原因"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="onCancel">取消</el-button>
        <el-button type="primary" @click="save">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import {ref, watch} from "vue";
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElButton,
  ElSelect,
  ElOption,
} from "element-plus";

const props = defineProps({
  visible: Boolean,
  field: Object,
  annotation: Object,
});
const emit = defineEmits(["save", "cancel"]);

// 下拉框和textarea的双向绑定
const selectedReason = ref("");
const textareaReason = ref("");

// 弹窗打开时重置内容
watch(
    () => props.visible,
    (val) => {
      if (val) {
        selectedReason.value = "";
        textareaReason.value = "";
      }
    }
);

// 保存
function save() {
  // 优先textarea，否则用下拉框
  const result = textareaReason.value.trim() || selectedReason.value.trim();
  // props.field.warning = result;

  props.annotation.approval = result;

  emit("save", JSON.parse(JSON.stringify(props.annotation)));
}

function handleReasonChange() {
  textareaReason.value = selectedReason.value;
}

// 取消
function onCancel() {
  emit("cancel");
}
</script>

<style scoped>
.field-annotation-dialog :deep(.el-dialog__header) {
  padding: 16px 20px;
  margin-right: 0;
  border-bottom: 1px solid #ebeef5;
  background-color: #f5f7fa;
}

.field-annotation-dialog :deep(.el-dialog__title) {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.field-annotation-dialog :deep(.el-dialog__footer) {
  padding: 12px 20px;
  border-top: 1px solid #ebeef5;
  background-color: #f5f7fa;
}

.annotation-form {
  padding-top: 8px;
}

.annotation-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #333;
}

.annotation-form :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
}

.annotation-form :deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #c0c4cc inset;
}

.annotation-form :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409eff inset;
}

/* 单位输入框样式 */
.unit-input-container {
  display: flex;
  align-items: center;
  width: 100%;
}

.unit-btn {
  padding: 8px 12px;
  font-weight: bold;
}

.unit-input {
  margin: 0 8px;
  width: 100%;
}

.unit-input :deep(.el-input__wrapper) {
  text-align: center;
}

.unit-input :deep(.el-input__inner) {
  text-align: center;
}

/* 选项相关样式已移至 OptionsPropertyEditor.vue */

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
