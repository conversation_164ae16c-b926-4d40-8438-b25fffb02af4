<template>
  <div class="navbar">
    <div class="left-menu">
      <div class="logo-container">
        <img src="/img/logo.png" alt="Logo" class="logo"/>
        <span class="title"></span>
      </div>
      <!-- <breadcrumb class="breadcrumb-container" /> -->
    </div>

    <div class="right-menu">
      <LangSelect/>

      <!-- 用户头像和下拉菜单 -->
      <el-dropdown class="avatar-container" trigger="click">
        <div class="avatar-wrapper">
          <ImagePreview
              :file="userStore.avatar"
              :size="32"
              fit="cover"
              defaultUrl="/img/default-avatar.svg"
              :cycle="true"
          />

          <span class="username">{{ userName }}</span>
          <el-icon>
            <caret-bottom/>
          </el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <router-link to="/profile">
              <el-dropdown-item>个人中心</el-dropdown-item>
            </router-link>
            <el-dropdown-item divided @click="logout">
              <span>退出登录</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
import {computed} from "vue";
import {useRouter} from "vue-router";
import {useUserStore} from "@/stores/user";
import {useI18n} from "vue-i18n";
import {CaretBottom} from "@element-plus/icons-vue";

const router = useRouter();
const userStore = useUserStore();
const {locale} = useI18n();

// 用户名和头像
const userName = computed(() => {
  return userStore.name || "用户";
});

const userAvatar = computed(() => {
  return userStore.avatar || "/img/default-avatar.svg";
});


// 退出登录
const logout = async () => {
  await userStore.logout();
  await router.push({
        name: "login"
      }
  );
};
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  z-index: 1000;

  .left-menu {
    display: flex;
    align-items: center;
    height: 100%;

    .logo-container {
      display: flex;
      align-items: center;
      margin-right: 20px;

      .logo {
        height: 32px;
        margin-right: 10px;
      }

      .title {
        font-size: 18px;
        font-weight: bold;
        color: #303133;
      }
    }

    .breadcrumb-container {
      display: inline-block;
    }
  }

  .right-menu {
    display: flex;
    align-items: center;
    height: 100%;

    .language-container {
      margin-right: 20px;

      .language-wrapper {
        display: flex;
        align-items: center;
        cursor: pointer;

        i {
          font-size: 18px;
          color: #5a5e66;
          margin-right: 5px;
        }

        .language-text {
          margin: 0 5px;
          color: #5a5e66;
        }

        .el-icon {
          font-size: 12px;
          color: #5a5e66;
        }
      }
    }

    .avatar-container {
      .avatar-wrapper {
        display: flex;
        align-items: center;
        cursor: pointer;

        .user-avatar {
          width: 32px;
          height: 32px;
          border-radius: 8px;
          object-fit: cover;
        }

        .username {
          margin: 0 5px;
          color: #5a5e66;
        }

        .el-icon {
          font-size: 12px;
          color: #5a5e66;
        }
      }
    }
  }
}
</style>
